import { getApiBaseUrl } from "./config";

const API_BASE_URL = getApiBaseUrl();

/**
 * Order status enumeration
 */
export type OrderStatus =
  | "pending"
  | "paid"
  | "processing"
  | "shipped"
  | "delivered"
  | "cancelled"
  | "refunded"
  | "returned"
  | "failed";

/**
 * Shipping status enumeration
 */
export type ShippingStatus =
  | "pending"
  | "preparing"
  | "shipped"
  | "in-transit"
  | "delivered"
  | "returned"
  | "cancelled";

/**
 * Payment status enumeration
 */
export type PaymentStatus =
  | "pending"
  | "paid"
  | "failed"
  | "refunded"
  | "partially-refunded";

/**
 * Payment method enumeration
 */
export type PaymentMethod =
  | "card"
  | "paypal"
  | "bank-transfer"
  | "cash-on-delivery"
  | "crypto";

/**
 * Currency enumeration
 */
export type CurrencyUnit = "EUR" | "USD";

/**
 * Order item interface
 */
export interface OrderItem {
  productId: string;
  name: string;
  quantity: number;
  price: number;
  currency: CurrencyUnit;
  image?: string;
  sku?: string;
  variant?: string;
}

/**
 * Customer information interface
 */
export interface Customer {
  customerId?: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
}

/**
 * Address interface
 */
export interface Address {
  street: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

/**
 * Shipping information interface
 */
export interface Shipping {
  method: string;
  cost: number;
  trackingNumber?: string;
  estimatedDelivery?: string;
  shippedAt?: string;
  deliveredAt?: string;
}

/**
 * Payment information interface
 */
export interface Payment {
  method: PaymentMethod;
  status: PaymentStatus;
  transactionId?: string;
  providerTransactionId?: string;
  cardLast4?: string;
  cardType?: string;
  receiptUrl?: string;
  paidAt?: string;
}

/**
 * Order interface matching the backend Order type
 */
export interface Order {
  _id: string;
  orderNumber: string;
  customer: Customer;
  items: OrderItem[];
  
  // Pricing
  subtotal: number;
  shippingCost: number;
  tax: number;
  discount: number;
  totalAmount: number;
  currency: CurrencyUnit;
  
  // Status
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  shippingStatus: ShippingStatus;
  
  // Addresses
  shippingAddress: Address;
  billingAddress?: Address;
  
  // Payment & Shipping
  payment: Payment;
  shipping: Shipping;
  
  // Additional info
  notes?: string;
  internalNotes?: string;
  tags?: string[];
  
  // Timestamps
  placedAt: string;
  estimatedDelivery?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * DTO for creating a new order
 */
export interface CreateOrderDto {
  customer: Customer;
  items: OrderItem[];
  shippingAddress: Address;
  billingAddress?: Address;
  shipping: {
    method: string;
    cost: number;
  };
  payment: {
    method: PaymentMethod;
    transactionId?: string;
  };
  notes?: string;
  currency?: CurrencyUnit;
}

/**
 * DTO for updating an order
 */
export interface UpdateOrderDto {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  shippingStatus?: ShippingStatus;
  shipping?: Partial<Shipping>;
  payment?: Partial<Payment>;
  notes?: string;
  internalNotes?: string;
  tags?: string[];
}

/**
 * Order filters for querying
 */
export interface OrderFilters {
  status?: OrderStatus | OrderStatus[];
  paymentStatus?: PaymentStatus | PaymentStatus[];
  shippingStatus?: ShippingStatus | ShippingStatus[];
  customerId?: string;
  customerEmail?: string;
  orderNumber?: string;
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
}

/**
 * Order statistics interface
 */
export interface OrderStats {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  ordersByStatus: Record<OrderStatus, number>;
  ordersByPaymentStatus: Record<PaymentStatus, number>;
  ordersByShippingStatus: Record<ShippingStatus, number>;
  recentOrders: number;
  pendingOrders: number;
}

/**
 * Paginated orders response
 */
export interface OrdersResponse {
  orders: Order[];
  total: number;
  page: number;
  totalPages: number;
}

/**
 * API response wrapper
 */
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

/**
 * Get all orders with optional filtering and pagination
 */
export const getOrders = async (
  filters: OrderFilters = {},
  page: number = 1,
  limit: number = 20,
  sortBy: string = "placedAt",
  sortOrder: "asc" | "desc" = "desc"
): Promise<OrdersResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    sortBy,
    sortOrder,
  });

  // Add filters to params
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      if (Array.isArray(value)) {
        value.forEach((v) => params.append(key, v.toString()));
      } else {
        params.append(key, value.toString());
      }
    }
  });

  const response = await fetch(`${API_BASE_URL}/orders?${params}`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch orders: ${response.statusText}`);
  }

  const result: ApiResponse<OrdersResponse> = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || "Failed to fetch orders");
  }

  return result.data;
};

/**
 * Get order by ID
 */
export const getOrderById = async (orderId: string): Promise<Order> => {
  const response = await fetch(`${API_BASE_URL}/orders/${orderId}`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch order: ${response.statusText}`);
  }

  const result: ApiResponse<Order> = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || "Failed to fetch order");
  }

  return result.data;
};

/**
 * Get order by order number
 */
export const getOrderByNumber = async (orderNumber: string): Promise<Order> => {
  const response = await fetch(`${API_BASE_URL}/orders/number/${orderNumber}`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch order: ${response.statusText}`);
  }

  const result: ApiResponse<Order> = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || "Failed to fetch order");
  }

  return result.data;
};

/**
 * Create a new order
 */
export const createOrder = async (orderData: CreateOrderDto): Promise<Order> => {
  const response = await fetch(`${API_BASE_URL}/orders`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(orderData),
  });

  if (!response.ok) {
    throw new Error(`Failed to create order: ${response.statusText}`);
  }

  const result: ApiResponse<Order> = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || "Failed to create order");
  }

  return result.data;
};

/**
 * Update an order
 */
export const updateOrder = async (orderId: string, updateData: UpdateOrderDto): Promise<Order> => {
  const response = await fetch(`${API_BASE_URL}/orders/${orderId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(updateData),
  });

  if (!response.ok) {
    throw new Error(`Failed to update order: ${response.statusText}`);
  }

  const result: ApiResponse<Order> = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || "Failed to update order");
  }

  return result.data;
};

/**
 * Update order status
 */
export const updateOrderStatus = async (
  orderId: string,
  status: OrderStatus,
  paymentStatus?: PaymentStatus,
  shippingStatus?: ShippingStatus
): Promise<Order> => {
  const response = await fetch(`${API_BASE_URL}/orders/${orderId}/status`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ status, paymentStatus, shippingStatus }),
  });

  if (!response.ok) {
    throw new Error(`Failed to update order status: ${response.statusText}`);
  }

  const result: ApiResponse<Order> = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || "Failed to update order status");
  }

  return result.data;
};

/**
 * Delete (cancel) an order
 */
export const deleteOrder = async (orderId: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/orders/${orderId}`, {
    method: "DELETE",
  });

  if (!response.ok) {
    throw new Error(`Failed to delete order: ${response.statusText}`);
  }

  const result: ApiResponse<void> = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || "Failed to delete order");
  }
};

/**
 * Get order statistics
 */
export const getOrderStats = async (dateFrom?: string, dateTo?: string): Promise<OrderStats> => {
  const params = new URLSearchParams();
  if (dateFrom) params.append("dateFrom", dateFrom);
  if (dateTo) params.append("dateTo", dateTo);

  const response = await fetch(`${API_BASE_URL}/orders/stats?${params}`);
  
  if (!response.ok) {
    throw new Error(`Failed to fetch order statistics: ${response.statusText}`);
  }

  const result: ApiResponse<OrderStats> = await response.json();
  
  if (!result.success) {
    throw new Error(result.error || "Failed to fetch order statistics");
  }

  return result.data;
};
