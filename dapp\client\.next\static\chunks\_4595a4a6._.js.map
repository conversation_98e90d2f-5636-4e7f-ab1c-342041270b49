{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersListActions.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Plus, Download, RefreshCw } from \"lucide-react\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nexport const OrdersListActions = () => {\r\n  return (\r\n    <div className=\"flex gap-2\">\r\n      <Button variant=\"outline\" size=\"sm\">\r\n        <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n        Refresh\r\n      </Button>\r\n      \r\n      <Button variant=\"outline\" size=\"sm\">\r\n        <Download className=\"mr-2 h-4 w-4\" />\r\n        Export All\r\n      </Button>\r\n      \r\n      <Button size=\"sm\">\r\n        <Plus className=\"mr-2 h-4 w-4\" />\r\n        New Order\r\n      </Button>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAEA;AALA;;;;AAOO,MAAM,oBAAoB;IAC/B,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC,8HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;;kCAC7B,4TAAC,uSAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAIxC,4TAAC,8HAAA,CAAA,SAAM;gBAAC,SAAQ;gBAAU,MAAK;;kCAC7B,4TAAC,iSAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAIvC,4TAAC,8HAAA,CAAA,SAAM;gBAAC,MAAK;;kCACX,4TAAC,yRAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAKzC;KAnBa", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,4TAAC,+QAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,+QAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,4TAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kRAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,4TAAC,kRAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,4TAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,uSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,kRAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,2SAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,kRAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,4TAAC,kRAAA,CAAA,SAAsB;kBACrB,cAAA,4TAAC,kRAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,4TAAC;;;;;8BACD,4TAAC,kRAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,4TAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,kRAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,kRAAA,CAAA,gBAA6B;8BAC5B,cAAA,4TAAC,2RAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,4TAAC,kRAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kRAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,kRAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersFilter.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Search, Filter, Calendar, Download } from \"lucide-react\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\n\r\nexport const OrdersFilter = () => {\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Search and Quick Actions */}\r\n      <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n        <div className=\"relative flex-1 max-w-md\">\r\n          <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\r\n          <Input\r\n            placeholder=\"Search orders by ID, customer name, or email...\"\r\n            className=\"pl-10\"\r\n          />\r\n        </div>\r\n        \r\n        <div className=\"flex gap-2\">\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            <Calendar className=\"mr-2 h-4 w-4\" />\r\n            Date Range\r\n          </Button>\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            <Download className=\"mr-2 h-4 w-4\" />\r\n            Export\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <div className=\"flex flex-wrap gap-4\">\r\n        <Select defaultValue=\"all\">\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <Filter className=\"mr-2 h-4 w-4\" />\r\n            <SelectValue placeholder=\"Status\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Status</SelectItem>\r\n            <SelectItem value=\"pending\">Pending</SelectItem>\r\n            <SelectItem value=\"paid\">Paid</SelectItem>\r\n            <SelectItem value=\"shipped\">Shipped</SelectItem>\r\n            <SelectItem value=\"delivered\">Delivered</SelectItem>\r\n            <SelectItem value=\"cancelled\">Cancelled</SelectItem>\r\n            <SelectItem value=\"refunded\">Refunded</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Select defaultValue=\"all\">\r\n          <SelectTrigger className=\"w-[160px]\">\r\n            <SelectValue placeholder=\"Shipping Status\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Shipping</SelectItem>\r\n            <SelectItem value=\"pending\">Pending</SelectItem>\r\n            <SelectItem value=\"processing\">Processing</SelectItem>\r\n            <SelectItem value=\"shipped\">Shipped</SelectItem>\r\n            <SelectItem value=\"delivered\">Delivered</SelectItem>\r\n            <SelectItem value=\"returned\">Returned</SelectItem>\r\n            <SelectItem value=\"cancelled\">Cancelled</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n\r\n        <Select defaultValue=\"newest\">\r\n          <SelectTrigger className=\"w-[140px]\">\r\n            <SelectValue placeholder=\"Sort by\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"newest\">Newest First</SelectItem>\r\n            <SelectItem value=\"oldest\">Oldest First</SelectItem>\r\n            <SelectItem value=\"amount-high\">Amount: High to Low</SelectItem>\r\n            <SelectItem value=\"amount-low\">Amount: Low to High</SelectItem>\r\n            <SelectItem value=\"customer\">Customer Name</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAPA;;;;;;AASO,MAAM,eAAe;IAC1B,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,6RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,4TAAC,6HAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAId,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,4TAAC,iSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,4TAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,4TAAC,iSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,4TAAC;gBAAI,WAAU;;kCACb,4TAAC,8HAAA,CAAA,SAAM;wBAAC,cAAa;;0CACnB,4TAAC,8HAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,4TAAC,6RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,4TAAC,8HAAA,CAAA,cAAW;wCAAC,aAAY;;;;;;;;;;;;0CAE3B,4TAAC,8HAAA,CAAA,gBAAa;;kDACZ,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAO;;;;;;kDACzB,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;;;;;;;;;;;;;kCAIjC,4TAAC,8HAAA,CAAA,SAAM;wBAAC,cAAa;;0CACnB,4TAAC,8HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,4TAAC,8HAAA,CAAA,gBAAa;;kDACZ,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAa;;;;;;kDAC/B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;;;;;;;;;;;;;kCAIlC,4TAAC,8HAAA,CAAA,SAAM;wBAAC,cAAa;;0CACnB,4TAAC,8HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,4TAAC,8HAAA,CAAA,gBAAa;;kDACZ,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;kDAC3B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;kDAC3B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAc;;;;;;kDAChC,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAa;;;;;;kDAC/B,4TAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;KAzEa", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf;KAVS", "debugId": null}}, {"offset": {"line": 898, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/orders.ts"], "sourcesContent": ["const getApiBaseUrl = (): string => {\n  if (typeof window !== \"undefined\") {\n    // Client-side: use current origin or environment variable\n    return (\n      process.env.NEXT_PUBLIC_API_URL ||\n      `${window.location.protocol}//${window.location.hostname}:3001`\n    );\n  }\n  // Server-side: use environment variable or default\n  return process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\";\n};\n\nconst API_BASE_URL = getApiBaseUrl();\n\n/**\n * Order status enumeration\n */\nexport type OrderStatus =\n  | \"pending\"\n  | \"paid\"\n  | \"processing\"\n  | \"shipped\"\n  | \"delivered\"\n  | \"cancelled\"\n  | \"refunded\"\n  | \"returned\"\n  | \"failed\";\n\n/**\n * Shipping status enumeration\n */\nexport type ShippingStatus =\n  | \"pending\"\n  | \"preparing\"\n  | \"shipped\"\n  | \"in-transit\"\n  | \"delivered\"\n  | \"returned\"\n  | \"cancelled\";\n\n/**\n * Payment status enumeration\n */\nexport type PaymentStatus =\n  | \"pending\"\n  | \"paid\"\n  | \"failed\"\n  | \"refunded\"\n  | \"partially-refunded\";\n\n/**\n * Payment method enumeration\n */\nexport type PaymentMethod =\n  | \"card\"\n  | \"paypal\"\n  | \"bank-transfer\"\n  | \"cash-on-delivery\"\n  | \"crypto\";\n\n/**\n * Currency enumeration\n */\nexport type CurrencyUnit = \"EUR\" | \"USD\";\n\n/**\n * Order item interface\n */\nexport interface OrderItem {\n  productId: string;\n  name: string;\n  quantity: number;\n  price: number;\n  currency: CurrencyUnit;\n  image?: string;\n  sku?: string;\n  variant?: string;\n}\n\n/**\n * Customer information interface\n */\nexport interface Customer {\n  customerId?: string;\n  name: string;\n  email: string;\n  phone?: string;\n  avatar?: string;\n}\n\n/**\n * Address interface\n */\nexport interface Address {\n  street: string;\n  city: string;\n  state?: string;\n  postalCode: string;\n  country: string;\n  coordinates?: {\n    latitude: number;\n    longitude: number;\n  };\n}\n\n/**\n * Shipping information interface\n */\nexport interface Shipping {\n  method: string;\n  cost: number;\n  trackingNumber?: string;\n  estimatedDelivery?: string;\n  shippedAt?: string;\n  deliveredAt?: string;\n}\n\n/**\n * Payment information interface\n */\nexport interface Payment {\n  method: PaymentMethod;\n  status: PaymentStatus;\n  transactionId?: string;\n  providerTransactionId?: string;\n  cardLast4?: string;\n  cardType?: string;\n  receiptUrl?: string;\n  paidAt?: string;\n}\n\n/**\n * Order interface matching the backend Order type\n */\nexport interface Order {\n  _id: string;\n  orderNumber: string;\n  customer: Customer;\n  items: OrderItem[];\n\n  // Pricing\n  subtotal: number;\n  shippingCost: number;\n  tax: number;\n  discount: number;\n  totalAmount: number;\n  currency: CurrencyUnit;\n\n  // Status\n  status: OrderStatus;\n  paymentStatus: PaymentStatus;\n  shippingStatus: ShippingStatus;\n\n  // Addresses\n  shippingAddress: Address;\n  billingAddress?: Address;\n\n  // Payment & Shipping\n  payment: Payment;\n  shipping: Shipping;\n\n  // Additional info\n  notes?: string;\n  internalNotes?: string;\n  tags?: string[];\n\n  // Timestamps\n  placedAt: string;\n  estimatedDelivery?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * DTO for creating a new order\n */\nexport interface CreateOrderDto {\n  customer: Customer;\n  items: OrderItem[];\n  shippingAddress: Address;\n  billingAddress?: Address;\n  shipping: {\n    method: string;\n    cost: number;\n  };\n  payment: {\n    method: PaymentMethod;\n    transactionId?: string;\n  };\n  notes?: string;\n  currency?: CurrencyUnit;\n}\n\n/**\n * DTO for updating an order\n */\nexport interface UpdateOrderDto {\n  status?: OrderStatus;\n  paymentStatus?: PaymentStatus;\n  shippingStatus?: ShippingStatus;\n  shipping?: Partial<Shipping>;\n  payment?: Partial<Payment>;\n  notes?: string;\n  internalNotes?: string;\n  tags?: string[];\n}\n\n/**\n * Order filters for querying\n */\nexport interface OrderFilters {\n  status?: OrderStatus | OrderStatus[];\n  paymentStatus?: PaymentStatus | PaymentStatus[];\n  shippingStatus?: ShippingStatus | ShippingStatus[];\n  customerId?: string;\n  customerEmail?: string;\n  orderNumber?: string;\n  dateFrom?: string;\n  dateTo?: string;\n  minAmount?: number;\n  maxAmount?: number;\n  search?: string;\n}\n\n/**\n * Order statistics interface\n */\nexport interface OrderStats {\n  totalOrders: number;\n  totalRevenue: number;\n  averageOrderValue: number;\n  ordersByStatus: Record<OrderStatus, number>;\n  ordersByPaymentStatus: Record<PaymentStatus, number>;\n  ordersByShippingStatus: Record<ShippingStatus, number>;\n  recentOrders: number;\n  pendingOrders: number;\n}\n\n/**\n * Paginated orders response\n */\nexport interface OrdersResponse {\n  orders: Order[];\n  total: number;\n  page: number;\n  totalPages: number;\n}\n\n/**\n * API response wrapper\n */\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n  error?: string;\n}\n\n/**\n * Get all orders with optional filtering and pagination\n */\nexport const getOrders = async (\n  filters: OrderFilters = {},\n  page: number = 1,\n  limit: number = 20,\n  sortBy: string = \"placedAt\",\n  sortOrder: \"asc\" | \"desc\" = \"desc\"\n): Promise<OrdersResponse> => {\n  const params = new URLSearchParams({\n    page: page.toString(),\n    limit: limit.toString(),\n    sortBy,\n    sortOrder,\n  });\n\n  // Add filters to params\n  Object.entries(filters).forEach(([key, value]) => {\n    if (value !== undefined && value !== null && value !== \"\") {\n      if (Array.isArray(value)) {\n        value.forEach((v) => params.append(key, v.toString()));\n      } else {\n        params.append(key, value.toString());\n      }\n    }\n  });\n\n  const response = await fetch(`${API_BASE_URL}/orders?${params}`);\n\n  if (!response.ok) {\n    throw new Error(`Failed to fetch orders: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<OrdersResponse> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to fetch orders\");\n  }\n\n  return result.data;\n};\n\n/**\n * Get order by ID\n */\nexport const getOrderById = async (orderId: string): Promise<Order> => {\n  const response = await fetch(`${API_BASE_URL}/orders/${orderId}`);\n\n  if (!response.ok) {\n    throw new Error(`Failed to fetch order: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<Order> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to fetch order\");\n  }\n\n  return result.data;\n};\n\n/**\n * Get order by order number\n */\nexport const getOrderByNumber = async (orderNumber: string): Promise<Order> => {\n  const response = await fetch(`${API_BASE_URL}/orders/number/${orderNumber}`);\n\n  if (!response.ok) {\n    throw new Error(`Failed to fetch order: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<Order> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to fetch order\");\n  }\n\n  return result.data;\n};\n\n/**\n * Create a new order\n */\nexport const createOrder = async (\n  orderData: CreateOrderDto\n): Promise<Order> => {\n  const response = await fetch(`${API_BASE_URL}/orders`, {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: JSON.stringify(orderData),\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to create order: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<Order> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to create order\");\n  }\n\n  return result.data;\n};\n\n/**\n * Update an order\n */\nexport const updateOrder = async (\n  orderId: string,\n  updateData: UpdateOrderDto\n): Promise<Order> => {\n  const response = await fetch(`${API_BASE_URL}/orders/${orderId}`, {\n    method: \"PUT\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: JSON.stringify(updateData),\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to update order: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<Order> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to update order\");\n  }\n\n  return result.data;\n};\n\n/**\n * Update order status\n */\nexport const updateOrderStatus = async (\n  orderId: string,\n  status: OrderStatus,\n  paymentStatus?: PaymentStatus,\n  shippingStatus?: ShippingStatus\n): Promise<Order> => {\n  const response = await fetch(`${API_BASE_URL}/orders/${orderId}/status`, {\n    method: \"PUT\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: JSON.stringify({ status, paymentStatus, shippingStatus }),\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to update order status: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<Order> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to update order status\");\n  }\n\n  return result.data;\n};\n\n/**\n * Delete (cancel) an order\n */\nexport const deleteOrder = async (orderId: string): Promise<void> => {\n  const response = await fetch(`${API_BASE_URL}/orders/${orderId}`, {\n    method: \"DELETE\",\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to delete order: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<void> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to delete order\");\n  }\n};\n\n/**\n * Get order statistics\n */\nexport const getOrderStats = async (\n  dateFrom?: string,\n  dateTo?: string\n): Promise<OrderStats> => {\n  const params = new URLSearchParams();\n  if (dateFrom) params.append(\"dateFrom\", dateFrom);\n  if (dateTo) params.append(\"dateTo\", dateTo);\n\n  const response = await fetch(`${API_BASE_URL}/orders/stats?${params}`);\n\n  if (!response.ok) {\n    throw new Error(`Failed to fetch order statistics: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<OrderStats> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to fetch order statistics\");\n  }\n\n  return result.data;\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAIM;AAJN,MAAM,gBAAgB;IACpB,wCAAmC;QACjC,0DAA0D;QAC1D,OACE,6DACA,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;IAEnE;;AAGF;AAEA,MAAM,eAAe;AAyPd,MAAM,YAAY,OACvB,UAAwB,CAAC,CAAC,EAC1B,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAAiB,UAAU,EAC3B,YAA4B,MAAM;IAElC,MAAM,SAAS,IAAI,gBAAgB;QACjC,MAAM,KAAK,QAAQ;QACnB,OAAO,MAAM,QAAQ;QACrB;QACA;IACF;IAEA,wBAAwB;IACxB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAC,IAAM,OAAO,MAAM,CAAC,KAAK,EAAE,QAAQ;YACpD,OAAO;gBACL,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;IACF;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ;IAE/D,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;IAClE;IAEA,MAAM,SAAsC,MAAM,SAAS,IAAI;IAE/D,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS;IAEhE,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;IACjE;IAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;IAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,mBAAmB,OAAO;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,EAAE,aAAa;IAE3E,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;IACjE;IAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;IAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,cAAc,OACzB;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,CAAC,EAAE;QACrD,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;IAClE;IAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;IAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,cAAc,OACzB,SACA;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;QAChE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;IAClE;IAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;IAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,oBAAoB,OAC/B,SACA,QACA,eACA;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,OAAO,CAAC,EAAE;QACvE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;YAAQ;YAAe;QAAe;IAC/D;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,UAAU,EAAE;IACzE;IAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;IAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;QAChE,QAAQ;IACV;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;IAClE;IAEA,MAAM,SAA4B,MAAM,SAAS,IAAI;IAErD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;AACF;AAKO,MAAM,gBAAgB,OAC3B,UACA;IAEA,MAAM,SAAS,IAAI;IACnB,IAAI,UAAU,OAAO,MAAM,CAAC,YAAY;IACxC,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;IAEpC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,EAAE,QAAQ;IAErE,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;IAC5E;IAEA,MAAM,SAAkC,MAAM,SAAS,IAAI;IAE3D,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB", "debugId": null}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useOrders.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from \"react\";\nimport { toast } from \"sonner\";\nimport {\n  Order,\n  OrdersResponse,\n  OrderFilters,\n  OrderStats,\n  CreateOrderDto,\n  UpdateOrderDto,\n  OrderStatus,\n  PaymentStatus,\n  ShippingStatus,\n  getOrders,\n  getOrderById,\n  getOrderByNumber,\n  createOrder,\n  updateOrder,\n  updateOrderStatus,\n  deleteOrder,\n  getOrderStats,\n} from \"@/lib/api/orders\";\n\n/**\n * Hook for fetching orders with filtering and pagination\n */\nexport const useOrders = (\n  initialFilters: OrderFilters = {},\n  initialPage: number = 1,\n  initialLimit: number = 20,\n  initialSortBy: string = \"placedAt\",\n  initialSortOrder: \"asc\" | \"desc\" = \"desc\"\n) => {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [total, setTotal] = useState(0);\n  const [page, setPage] = useState(initialPage);\n  const [totalPages, setTotalPages] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [filters, setFilters] = useState<OrderFilters>(initialFilters);\n  const [limit] = useState(initialLimit);\n  const [sortBy] = useState(initialSortBy);\n  const [sortOrder] = useState(initialSortOrder);\n\n  const fetchOrders = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await getOrders(filters, page, limit, sortBy, sortOrder);\n      setOrders(response.orders);\n      setTotal(response.total);\n      setTotalPages(response.totalPages);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch orders\";\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [filters, page, limit, sortBy, sortOrder]);\n\n  useEffect(() => {\n    fetchOrders();\n  }, [fetchOrders]);\n\n  const refetch = useCallback(() => {\n    fetchOrders();\n  }, [fetchOrders]);\n\n  const updateFilters = useCallback((newFilters: OrderFilters) => {\n    setFilters(newFilters);\n    setPage(1); // Reset to first page when filters change\n  }, []);\n\n  const updatePage = useCallback((newPage: number) => {\n    setPage(newPage);\n  }, []);\n\n  return {\n    orders,\n    total,\n    page,\n    totalPages,\n    loading,\n    error,\n    filters,\n    refetch,\n    updateFilters,\n    updatePage,\n  };\n};\n\n/**\n * Hook for fetching a single order by ID\n */\nexport const useOrder = (orderId: string | null) => {\n  const [order, setOrder] = useState<Order | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchOrder = useCallback(async () => {\n    if (!orderId) return;\n\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const orderData = await getOrderById(orderId);\n      setOrder(orderData);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch order\";\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [orderId]);\n\n  useEffect(() => {\n    fetchOrder();\n  }, [fetchOrder]);\n\n  const refetch = useCallback(() => {\n    fetchOrder();\n  }, [fetchOrder]);\n\n  return {\n    order,\n    loading,\n    error,\n    refetch,\n  };\n};\n\n/**\n * Hook for fetching a single order by order number\n */\nexport const useOrderByNumber = (orderNumber: string | null) => {\n  const [order, setOrder] = useState<Order | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchOrder = useCallback(async () => {\n    if (!orderNumber) return;\n\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const orderData = await getOrderByNumber(orderNumber);\n      setOrder(orderData);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch order\";\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [orderNumber]);\n\n  useEffect(() => {\n    fetchOrder();\n  }, [fetchOrder]);\n\n  const refetch = useCallback(() => {\n    fetchOrder();\n  }, [fetchOrder]);\n\n  return {\n    order,\n    loading,\n    error,\n    refetch,\n  };\n};\n\n/**\n * Hook for order mutations (create, update, delete)\n */\nexport const useOrderMutations = () => {\n  const [loading, setLoading] = useState(false);\n\n  const handleCreateOrder = useCallback(async (orderData: CreateOrderDto): Promise<Order> => {\n    setLoading(true);\n    try {\n      const newOrder = await createOrder(orderData);\n      toast.success(\"Order created successfully\");\n      return newOrder;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to create order\";\n      toast.error(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const handleUpdateOrder = useCallback(async (orderId: string, updateData: UpdateOrderDto): Promise<Order> => {\n    setLoading(true);\n    try {\n      const updatedOrder = await updateOrder(orderId, updateData);\n      toast.success(\"Order updated successfully\");\n      return updatedOrder;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to update order\";\n      toast.error(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const handleUpdateOrderStatus = useCallback(async (\n    orderId: string,\n    status: OrderStatus,\n    paymentStatus?: PaymentStatus,\n    shippingStatus?: ShippingStatus\n  ): Promise<Order> => {\n    setLoading(true);\n    try {\n      const updatedOrder = await updateOrderStatus(orderId, status, paymentStatus, shippingStatus);\n      toast.success(\"Order status updated successfully\");\n      return updatedOrder;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to update order status\";\n      toast.error(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const handleDeleteOrder = useCallback(async (orderId: string): Promise<void> => {\n    setLoading(true);\n    try {\n      await deleteOrder(orderId);\n      toast.success(\"Order cancelled successfully\");\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to cancel order\";\n      toast.error(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  return {\n    loading,\n    createOrder: handleCreateOrder,\n    updateOrder: handleUpdateOrder,\n    updateOrderStatus: handleUpdateOrderStatus,\n    deleteOrder: handleDeleteOrder,\n  };\n};\n\n/**\n * Hook for fetching order statistics\n */\nexport const useOrderStats = (dateFrom?: string, dateTo?: string) => {\n  const [stats, setStats] = useState<OrderStats | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchStats = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const statsData = await getOrderStats(dateFrom, dateTo);\n      setStats(statsData);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch order statistics\";\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [dateFrom, dateTo]);\n\n  useEffect(() => {\n    fetchStats();\n  }, [fetchStats]);\n\n  const refetch = useCallback(() => {\n    fetchStats();\n  }, [fetchStats]);\n\n  return {\n    stats,\n    loading,\n    error,\n    refetch,\n  };\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAuBO,MAAM,YAAY,CACvB,iBAA+B,CAAC,CAAC,EACjC,cAAsB,CAAC,EACvB,eAAuB,EAAE,EACzB,gBAAwB,UAAU,EAClC,mBAAmC,MAAM;;IAEzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAgB;IACrD,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzB,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC1B,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE7B,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;8CAAE;YAC9B,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM,OAAO,QAAQ;gBAC/D,UAAU,SAAS,MAAM;gBACzB,SAAS,SAAS,KAAK;gBACvB,cAAc,SAAS,UAAU;YACnC,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;6CAAG;QAAC;QAAS;QAAM;QAAO;QAAQ;KAAU;IAE5C,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;QAAC;KAAY;IAEhB,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;0CAAE;YAC1B;QACF;yCAAG;QAAC;KAAY;IAEhB,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YACjC,WAAW;YACX,QAAQ,IAAI,0CAA0C;QACxD;+CAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YAC9B,QAAQ;QACV;4CAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAjEa;AAsEN,MAAM,WAAW,CAAC;;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4CAAE;YAC7B,IAAI,CAAC,SAAS;YAEd,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,YAAY,MAAM,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,EAAE;gBACrC,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;2CAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG;QAAC;KAAW;IAEf,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;yCAAE;YAC1B;QACF;wCAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IArCa;AA0CN,MAAM,mBAAmB,CAAC;;IAC/B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;oDAAE;YAC7B,IAAI,CAAC,aAAa;YAElB,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,YAAY,MAAM,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE;gBACzC,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;mDAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAW;IAEf,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;iDAAE;YAC1B;QACF;gDAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IArCa;AA0CN,MAAM,oBAAoB;;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4DAAE,OAAO;YAC3C,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;gBACnC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;2DAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4DAAE,OAAO,SAAiB;YAC5D,WAAW;YACX,IAAI;gBACF,MAAM,eAAe,MAAM,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,SAAS;gBAChD,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;2DAAG,EAAE;IAEL,MAAM,0BAA0B,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kEAAE,OAC1C,SACA,QACA,eACA;YAEA,WAAW;YACX,IAAI;gBACF,MAAM,eAAe,MAAM,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,QAAQ,eAAe;gBAC7E,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;iEAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4DAAE,OAAO;YAC3C,WAAW;YACX,IAAI;gBACF,MAAM,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;gBAClB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;2DAAG,EAAE;IAEL,OAAO;QACL;QACA,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,aAAa;IACf;AACF;IA1Ea;AA+EN,MAAM,gBAAgB,CAAC,UAAmB;;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAqB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;iDAAE;YAC7B,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,YAAY,MAAM,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;gBAChD,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;gDAAG;QAAC;QAAU;KAAO;IAErB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAW;IAEf,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;8CAAE;YAC1B;QACF;6CAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IAnCa", "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrderRow.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { format } from \"date-fns\";\r\nimport { Edit, Eye, MoreHorizontal, Package, Trash2, User } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Order } from \"@/lib/api/orders\";\r\n\r\ntype OrderRowProps = {\r\n  order: Order;\r\n};\r\n\r\nconst getStatusColor = (status: string) => {\r\n  switch (status) {\r\n    case \"paid\":\r\n      return \"bg-green-100 text-green-700 hover:bg-green-200\";\r\n    case \"pending\":\r\n      return \"bg-yellow-100 text-yellow-700 hover:bg-yellow-200\";\r\n    case \"shipped\":\r\n      return \"bg-blue-100 text-blue-700 hover:bg-blue-200\";\r\n    case \"delivered\":\r\n      return \"bg-emerald-100 text-emerald-700 hover:bg-emerald-200\";\r\n    case \"cancelled\":\r\n      return \"bg-red-100 text-red-700 hover:bg-red-200\";\r\n    case \"refunded\":\r\n      return \"bg-purple-100 text-purple-700 hover:bg-purple-200\";\r\n    default:\r\n      return \"bg-gray-100 text-gray-700 hover:bg-gray-200\";\r\n  }\r\n};\r\n\r\nconst getShippingStatusColor = (status: string) => {\r\n  switch (status) {\r\n    case \"shipped\":\r\n      return \"bg-blue-100 text-blue-700 hover:bg-blue-200\";\r\n    case \"delivered\":\r\n      return \"bg-green-100 text-green-700 hover:bg-green-200\";\r\n    case \"pending\":\r\n      return \"bg-yellow-100 text-yellow-700 hover:bg-yellow-200\";\r\n    case \"processing\":\r\n      return \"bg-orange-100 text-orange-700 hover:bg-orange-200\";\r\n    case \"returned\":\r\n      return \"bg-purple-100 text-purple-700 hover:bg-purple-200\";\r\n    case \"cancelled\":\r\n      return \"bg-red-100 text-red-700 hover:bg-red-200\";\r\n    default:\r\n      return \"bg-gray-100 text-gray-700 hover:bg-gray-200\";\r\n  }\r\n};\r\n\r\nexport const OrderRow = ({ order }: OrderRowProps) => {\r\n  const router = useRouter();\r\n  const totalItems = order.items.reduce((sum, item) => sum + item.quantity, 0);\r\n  const displayItems = order.items.slice(0, 3); // Show max 3 items\r\n  const remainingItems = order.items.length - 3;\r\n\r\n  const handleViewDetails = () => {\r\n    router.push(`/admin/orders/${order._id}`);\r\n  };\r\n\r\n  const handleEditOrder = () => {\r\n    // TODO: Implement edit functionality\r\n    console.log(\"Edit order:\", order._id);\r\n  };\r\n\r\n  const handleCancelOrder = () => {\r\n    // TODO: Implement cancel functionality\r\n    console.log(\"Cancel order:\", order._id);\r\n  };\r\n\r\n  return (\r\n    <tr\r\n      className=\"cursor-pointer border-t transition-colors hover:bg-gray-50/50\"\r\n      onClick={handleViewDetails}\r\n    >\r\n      {/* Order ID */}\r\n      <td className=\"p-4\" onClick={(e) => e.stopPropagation()}>\r\n        <Link\r\n          href={`/admin/orders/${order._id}`}\r\n          className=\"font-medium text-blue-600 hover:text-blue-800 hover:underline\"\r\n        >\r\n          #{order.orderNumber}\r\n        </Link>\r\n      </td>\r\n\r\n      {/* Customer */}\r\n      <td className=\"p-4\">\r\n        <div className=\"flex items-center gap-3\">\r\n          <div className=\"flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-gray-100\">\r\n            <User className=\"h-4 w-4 text-gray-500\" />\r\n          </div>\r\n          <div>\r\n            <div className=\"font-medium text-gray-900\">\r\n              {order.customer.name}\r\n            </div>\r\n            <div className=\"text-sm text-gray-500\">{order.customer.email}</div>\r\n          </div>\r\n        </div>\r\n      </td>\r\n\r\n      {/* Products */}\r\n      <td className=\"p-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex -space-x-2\">\r\n            {displayItems.map((item, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"flex h-8 w-8 shrink-0 overflow-hidden rounded-full border-2 border-white bg-gray-100\"\r\n              >\r\n                {item.image ? (\r\n                  <Image\r\n                    src={item.image}\r\n                    alt={item.name}\r\n                    width={32}\r\n                    height={32}\r\n                    className=\"object-cover\"\r\n                  />\r\n                ) : (\r\n                  <div className=\"flex h-full w-full items-center justify-center\">\r\n                    <Package className=\"h-4 w-4 text-gray-400\" />\r\n                  </div>\r\n                )}\r\n              </div>\r\n            ))}\r\n            {remainingItems > 0 && (\r\n              <div className=\"flex h-8 w-8 shrink-0 items-center justify-center rounded-full border-2 border-white bg-gray-200 text-xs font-medium text-gray-600\">\r\n                +{remainingItems}\r\n              </div>\r\n            )}\r\n          </div>\r\n          <span className=\"text-sm text-gray-500\">\r\n            {totalItems} item{totalItems !== 1 ? \"s\" : \"\"}\r\n          </span>\r\n        </div>\r\n      </td>\r\n\r\n      {/* Status */}\r\n      <td className=\"p-4 text-center\">\r\n        <Badge\r\n          className={`cursor-default font-normal ${getStatusColor(order.status)}`}\r\n        >\r\n          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}\r\n        </Badge>\r\n      </td>\r\n\r\n      {/* Shipping Status */}\r\n      <td className=\"p-4 text-center\">\r\n        <Badge\r\n          className={`cursor-default font-normal ${getShippingStatusColor(order.shippingStatus)}`}\r\n        >\r\n          {order.shippingStatus.charAt(0).toUpperCase() +\r\n            order.shippingStatus.slice(1)}\r\n        </Badge>\r\n      </td>\r\n\r\n      {/* Total */}\r\n      <td className=\"p-4 text-right\">\r\n        <div className=\"font-medium text-gray-900\">\r\n          {order.currency === \"EUR\" ? \"€\" : \"$\"}\r\n          {order.totalAmount.toFixed(2)}\r\n        </div>\r\n      </td>\r\n\r\n      {/* Date */}\r\n      <td className=\"p-4 text-center\">\r\n        <div className=\"text-sm text-gray-900\">\r\n          {format(new Date(order.placedAt), \"MMM dd, yyyy\")}\r\n        </div>\r\n        <div className=\"text-xs text-gray-500\">\r\n          {format(new Date(order.placedAt), \"HH:mm\")}\r\n        </div>\r\n      </td>\r\n\r\n      {/* Actions */}\r\n      <td className=\"p-4 text-center\" onClick={(e) => e.stopPropagation()}>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              <MoreHorizontal className=\"h-4 w-4\" />\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"end\">\r\n            <DropdownMenuItem onClick={handleViewDetails}>\r\n              <Eye className=\"mr-2 h-4 w-4\" />\r\n              View Details\r\n            </DropdownMenuItem>\r\n            <DropdownMenuItem onClick={handleEditOrder}>\r\n              <Edit className=\"mr-2 h-4 w-4\" />\r\n              Edit Order\r\n            </DropdownMenuItem>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem\r\n              className=\"text-red-600\"\r\n              onClick={handleCancelOrder}\r\n            >\r\n              <Trash2 className=\"mr-2 h-4 w-4\" />\r\n              Cancel Order\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </td>\r\n    </tr>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAZA;;;;;;;;;AAyBA,MAAM,iBAAiB,CAAC;IACtB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,yBAAyB,CAAC;IAC9B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,WAAW,CAAC,EAAE,KAAK,EAAiB;;IAC/C,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;IAC1E,MAAM,eAAe,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,mBAAmB;IACjE,MAAM,iBAAiB,MAAM,KAAK,CAAC,MAAM,GAAG;IAE5C,MAAM,oBAAoB;QACxB,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,MAAM,GAAG,EAAE;IAC1C;IAEA,MAAM,kBAAkB;QACtB,qCAAqC;QACrC,QAAQ,GAAG,CAAC,eAAe,MAAM,GAAG;IACtC;IAEA,MAAM,oBAAoB;QACxB,uCAAuC;QACvC,QAAQ,GAAG,CAAC,iBAAiB,MAAM,GAAG;IACxC;IAEA,qBACE,4TAAC;QACC,WAAU;QACV,SAAS;;0BAGT,4TAAC;gBAAG,WAAU;gBAAM,SAAS,CAAC,IAAM,EAAE,eAAe;0BACnD,cAAA,4TAAC,8RAAA,CAAA,UAAI;oBACH,MAAM,CAAC,cAAc,EAAE,MAAM,GAAG,EAAE;oBAClC,WAAU;;wBACX;wBACG,MAAM,WAAW;;;;;;;;;;;;0BAKvB,4TAAC;gBAAG,WAAU;0BACZ,cAAA,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC,yRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,4TAAC;;8CACC,4TAAC;oCAAI,WAAU;8CACZ,MAAM,QAAQ,CAAC,IAAI;;;;;;8CAEtB,4TAAC;oCAAI,WAAU;8CAAyB,MAAM,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMlE,4TAAC;gBAAG,WAAU;0BACZ,cAAA,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;gCACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,4TAAC;wCAEC,WAAU;kDAET,KAAK,KAAK,iBACT,4TAAC,+PAAA,CAAA,UAAK;4CACJ,KAAK,KAAK,KAAK;4CACf,KAAK,KAAK,IAAI;4CACd,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;iEAGZ,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,+RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;uCAblB;;;;;gCAkBR,iBAAiB,mBAChB,4TAAC;oCAAI,WAAU;;wCAAqI;wCAChJ;;;;;;;;;;;;;sCAIR,4TAAC;4BAAK,WAAU;;gCACb;gCAAW;gCAAM,eAAe,IAAI,MAAM;;;;;;;;;;;;;;;;;;0BAMjD,4TAAC;gBAAG,WAAU;0BACZ,cAAA,4TAAC,6HAAA,CAAA,QAAK;oBACJ,WAAW,CAAC,2BAA2B,EAAE,eAAe,MAAM,MAAM,GAAG;8BAEtE,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;0BAK/D,4TAAC;gBAAG,WAAU;0BACZ,cAAA,4TAAC,6HAAA,CAAA,QAAK;oBACJ,WAAW,CAAC,2BAA2B,EAAE,uBAAuB,MAAM,cAAc,GAAG;8BAEtF,MAAM,cAAc,CAAC,MAAM,CAAC,GAAG,WAAW,KACzC,MAAM,cAAc,CAAC,KAAK,CAAC;;;;;;;;;;;0BAKjC,4TAAC;gBAAG,WAAU;0BACZ,cAAA,4TAAC;oBAAI,WAAU;;wBACZ,MAAM,QAAQ,KAAK,QAAQ,MAAM;wBACjC,MAAM,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;0BAK/B,4TAAC;gBAAG,WAAU;;kCACZ,4TAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,QAAQ,GAAG;;;;;;kCAEpC,4TAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,QAAQ,GAAG;;;;;;;;;;;;0BAKtC,4TAAC;gBAAG,WAAU;gBAAkB,SAAS,CAAC,IAAM,EAAE,eAAe;0BAC/D,cAAA,4TAAC,wIAAA,CAAA,eAAY;;sCACX,4TAAC,wIAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,4TAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;0CAC1C,cAAA,4TAAC,uSAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAG9B,4TAAC,wIAAA,CAAA,sBAAmB;4BAAC,OAAM;;8CACzB,4TAAC,wIAAA,CAAA,mBAAgB;oCAAC,SAAS;;sDACzB,4TAAC,uRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGlC,4TAAC,wIAAA,CAAA,mBAAgB;oCAAC,SAAS;;sDACzB,4TAAC,kSAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,4TAAC,wIAAA,CAAA,wBAAqB;;;;;8CACtB,4TAAC,wIAAA,CAAA,mBAAgB;oCACf,WAAU;oCACV,SAAS;;sDAET,4TAAC,iSAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAzJa;;QACI,oQAAA,CAAA,YAAS;;;KADb", "debugId": null}}, {"offset": {"line": 1807, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersTable.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { Eye } from \"lucide-react\";\r\n\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { useOrders } from \"@/hooks/useOrders\";\r\n\r\nimport { OrderRow } from \"./OrderRow\";\r\n\r\nexport const OrdersTable = () => {\r\n  const { orders, loading, error } = useOrders();\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"rounded-lg border border-gray-200 bg-white p-8 text-center\">\r\n        <div className=\"text-red-600\">\r\n          <p>Error loading orders: {error}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"rounded-lg border border-gray-200 bg-white\">\r\n      <div className=\"overflow-x-auto\">\r\n        <table className=\"w-full text-sm\">\r\n          <thead>\r\n            <tr className=\"border-b bg-gray-50/50 text-gray-500\">\r\n              <th className=\"p-4 text-left font-medium\">Order</th>\r\n              <th className=\"p-4 text-left font-medium\">Customer</th>\r\n              <th className=\"p-4 text-left font-medium\">Products</th>\r\n              <th className=\"p-4 text-center font-medium\">Status</th>\r\n              <th className=\"p-4 text-center font-medium\">Shipping</th>\r\n              <th className=\"p-4 text-right font-medium\">Total</th>\r\n              <th className=\"p-4 text-center font-medium\">Date</th>\r\n              <th className=\"p-4 text-center font-medium\">Actions</th>\r\n            </tr>\r\n          </thead>\r\n\r\n          <tbody>\r\n            {loading\r\n              ? // Loading skeleton\r\n                Array.from({ length: 5 }).map((_, index) => (\r\n                  <tr key={index} className=\"border-t\">\r\n                    <td className=\"p-4\">\r\n                      <Skeleton className=\"h-4 w-20\" />\r\n                    </td>\r\n                    <td className=\"p-4\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <Skeleton className=\"h-8 w-8 rounded-full\" />\r\n                        <div className=\"space-y-1\">\r\n                          <Skeleton className=\"h-4 w-24\" />\r\n                          <Skeleton className=\"h-3 w-32\" />\r\n                        </div>\r\n                      </div>\r\n                    </td>\r\n                    <td className=\"p-4\">\r\n                      <Skeleton className=\"h-4 w-16\" />\r\n                    </td>\r\n                    <td className=\"p-4 text-center\">\r\n                      <Skeleton className=\"mx-auto h-6 w-16\" />\r\n                    </td>\r\n                    <td className=\"p-4 text-center\">\r\n                      <Skeleton className=\"mx-auto h-6 w-16\" />\r\n                    </td>\r\n                    <td className=\"p-4 text-right\">\r\n                      <Skeleton className=\"ml-auto h-4 w-16\" />\r\n                    </td>\r\n                    <td className=\"p-4 text-center\">\r\n                      <Skeleton className=\"mx-auto h-4 w-20\" />\r\n                    </td>\r\n                    <td className=\"p-4 text-center\">\r\n                      <Skeleton className=\"mx-auto h-8 w-8\" />\r\n                    </td>\r\n                  </tr>\r\n                ))\r\n              : orders.map((order) => (\r\n                  <OrderRow key={order._id} order={order} />\r\n                ))}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {/* Empty state - show when no orders */}\r\n      {!loading && orders.length === 0 && (\r\n        <div className=\"flex flex-col items-center justify-center py-12 text-center\">\r\n          <div className=\"mb-4 rounded-full bg-gray-100 p-3\">\r\n            <Eye className=\"h-6 w-6 text-gray-400\" />\r\n          </div>\r\n          <h3 className=\"mb-2 text-lg font-medium text-gray-900\">\r\n            No orders found\r\n          </h3>\r\n          <p className=\"text-gray-500\">\r\n            No orders match your current filters. Try adjusting your search\r\n            criteria.\r\n          </p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AAEA;AACA;AAEA;;;AATA;;;;;AAWO,MAAM,cAAc;;IACzB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD;IAE3C,IAAI,OAAO;QACT,qBACE,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;;wBAAE;wBAAuB;;;;;;;;;;;;;;;;;IAIlC;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAM,WAAU;;sCACf,4TAAC;sCACC,cAAA,4TAAC;gCAAG,WAAU;;kDACZ,4TAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,4TAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,4TAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,4TAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,4TAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,4TAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,4TAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,4TAAC;wCAAG,WAAU;kDAA8B;;;;;;;;;;;;;;;;;sCAIhD,4TAAC;sCACE,UAEG,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,4TAAC;oCAAe,WAAU;;sDACxB,4TAAC;4CAAG,WAAU;sDACZ,cAAA,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,4TAAC;4CAAG,WAAU;sDACZ,cAAA,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,gIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,4TAAC,gIAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAI1B,4TAAC;4CAAG,WAAU;sDACZ,cAAA,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,4TAAC;4CAAG,WAAU;sDACZ,cAAA,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,4TAAC;4CAAG,WAAU;sDACZ,cAAA,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,4TAAC;4CAAG,WAAU;sDACZ,cAAA,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,4TAAC;4CAAG,WAAU;sDACZ,cAAA,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,4TAAC;4CAAG,WAAU;sDACZ,cAAA,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;mCA7Bf;;;;4CAiCX,OAAO,GAAG,CAAC,CAAC,sBACV,4TAAC,qJAAA,CAAA,WAAQ;oCAAiB,OAAO;mCAAlB,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;YAOnC,CAAC,WAAW,OAAO,MAAM,KAAK,mBAC7B,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,uRAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,4TAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,4TAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAQvC;GA3Fa;;QACwB,qHAAA,CAAA,YAAS;;;KADjC", "debugId": null}}, {"offset": {"line": 2179, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersPagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Chevron<PERSON>eft, ChevronRight } from \"lucide-react\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\n\r\nexport const OrdersPagination = () => {\r\n  return (\r\n    <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n      {/* Results info */}\r\n      <div className=\"text-sm text-gray-500\">\r\n        Showing <span className=\"font-medium text-gray-900\">1-10</span> of{\" \"}\r\n        <span className=\"font-medium text-gray-900\">97</span> orders\r\n      </div>\r\n\r\n      {/* Pagination controls */}\r\n      <div className=\"flex items-center gap-4\">\r\n        {/* Items per page */}\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-sm text-gray-500\">Show:</span>\r\n          <Select defaultValue=\"10\">\r\n            <SelectTrigger className=\"w-[70px]\">\r\n              <SelectValue />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              <SelectItem value=\"10\">10</SelectItem>\r\n              <SelectItem value=\"25\">25</SelectItem>\r\n              <SelectItem value=\"50\">50</SelectItem>\r\n              <SelectItem value=\"100\">100</SelectItem>\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n\r\n        {/* Page navigation */}\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button variant=\"outline\" size=\"sm\" disabled>\r\n            <ChevronLeft className=\"h-4 w-4\" />\r\n            Previous\r\n          </Button>\r\n\r\n          {/* Page numbers */}\r\n          <div className=\"flex items-center gap-1\">\r\n            <Button variant=\"default\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              1\r\n            </Button>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              2\r\n            </Button>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              3\r\n            </Button>\r\n            <span className=\"px-2 text-sm text-gray-500\">...</span>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n              10\r\n            </Button>\r\n          </div>\r\n\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            Next\r\n            <ChevronRight className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAEA;AACA;AANA;;;;;AAQO,MAAM,mBAAmB;IAC9B,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;oBAAwB;kCAC7B,4TAAC;wBAAK,WAAU;kCAA4B;;;;;;oBAAW;oBAAI;kCACnE,4TAAC;wBAAK,WAAU;kCAA4B;;;;;;oBAAS;;;;;;;0BAIvD,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,4TAAC,8HAAA,CAAA,SAAM;gCAAC,cAAa;;kDACnB,4TAAC,8HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,4TAAC,8HAAA,CAAA,cAAW;;;;;;;;;;kDAEd,4TAAC,8HAAA,CAAA,gBAAa;;0DACZ,4TAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,4TAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,4TAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,4TAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAM9B,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,QAAQ;;kDAC1C,4TAAC,2SAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAKrC,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,4TAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,4TAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;kDAG5D,4TAAC;wCAAK,WAAU;kDAA6B;;;;;;kDAC7C,4TAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAK9D,4TAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;oCAAK;kDAElC,4TAAC,6SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;KA3Da", "debugId": null}}, {"offset": {"line": 2434, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/list/OrdersListWrapper.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\n\r\nimport { OrdersFilter } from \"./OrdersFilter\";\r\nimport { OrdersTable } from \"./OrdersTable\";\r\nimport { OrdersPagination } from \"./OrdersPagination\";\r\n\r\nexport const OrdersListWrapper = () => {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Card>\r\n        <CardContent className=\"p-6\">\r\n          <OrdersFilter />\r\n\r\n          <Separator className=\"my-3 mb-6\" />\r\n\r\n          <OrdersTable />\r\n        </CardContent>\r\n      </Card>\r\n\r\n      <OrdersPagination />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAEA;AACA;AACA;AATA;;;;;;;AAWO,MAAM,oBAAoB;IAC/B,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC,4HAAA,CAAA,OAAI;0BACH,cAAA,4TAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,4TAAC,yJAAA,CAAA,eAAY;;;;;sCAEb,4TAAC,iIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCAErB,4TAAC,wJAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0BAIhB,4TAAC,6JAAA,CAAA,mBAAgB;;;;;;;;;;;AAGvB;KAhBa", "debugId": null}}]}