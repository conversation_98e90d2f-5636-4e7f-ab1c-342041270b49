{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/common/PageHeaderWrapper.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ntype PageHeaderWrapperProps = {\r\n  title: string;\r\n  description: string;\r\n  children?: React.ReactNode;\r\n};\r\n\r\n/**\r\n * A standardized page header component with title and description\r\n * Used across admin pages for consistent UI\r\n */\r\nexport const PageHeaderWrapper = ({\r\n  title,\r\n  description,\r\n  children,\r\n}: PageHeaderWrapperProps) => {\r\n  return (\r\n    <div className=\"container mx-auto py-6\">\r\n      <div className=\"flex flex-col items-start justify-between gap-4 border-b pb-5 sm:flex-row sm:items-center sm:gap-0\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold tracking-tight\">{title}</h1>\r\n          <p className=\"text-muted-foreground\">{description}</p>\r\n        </div>\r\n        {children && <div>{children}</div>}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAYO,MAAM,oBAAoB,CAAC,EAChC,KAAK,EACL,WAAW,EACX,QAAQ,EACe;IACvB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;;sCACC,6WAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6WAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;;gBAEvC,0BAAY,6WAAC;8BAAK;;;;;;;;;;;;;;;;;AAI3B", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6WAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,8QAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,8QAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kUACA;QAED,GAAG,KAAK;;YAER;0BACD,6WAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,oSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,+QAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,+QAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6WAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6WAAC,+QAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6WAAC;;;;;8BACD,6WAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6WAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6WAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,6WAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,+QAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n))\r\nSwitch.displayName = SwitchPrimitives.Root.displayName\r\n\r\nexport { Switch }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+XACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6WAAC,+QAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,+QAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/brands/BrandManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nimport {\n  Archive,\n  Award,\n  Building2,\n  Copy,\n  Edit,\n  ExternalLink,\n  Eye,\n  Filter,\n  Globe,\n  Grid3X3,\n  Image,\n  List,\n  Mail,\n  MoreHorizontal,\n  Package,\n  Pencil,\n  Plus,\n  Save,\n  Search,\n  SortAsc,\n  SortDesc,\n  Star,\n  Tag,\n  Trash2,\n  TrendingUp,\n  X,\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\n\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Textarea } from \"@/components/ui/textarea\";\n\nexport type Brand = {\n  id: string;\n  name: string;\n  description: string;\n  slug: string;\n  logo?: string;\n  website?: string;\n  email?: string;\n  country?: string;\n  foundedYear?: number;\n  isActive: boolean;\n  isPremium: boolean;\n  productCount: number;\n  rating: number;\n  sortOrder: number;\n  createdAt: string;\n  updatedAt: string;\n};\n\n// Mock brands with enhanced data\nconst mockBrands: Brand[] = [\n  {\n    id: \"brand-1\",\n    name: \"Apple\",\n    description:\n      \"Innovative technology company known for premium consumer electronics\",\n    slug: \"apple\",\n    logo: \"🍎\",\n    website: \"https://apple.com\",\n    email: \"<EMAIL>\",\n    country: \"United States\",\n    foundedYear: 1976,\n    isActive: true,\n    isPremium: true,\n    productCount: 89,\n    rating: 4.8,\n    sortOrder: 1,\n    createdAt: \"2024-01-01T00:00:00Z\",\n    updatedAt: \"2024-01-15T10:30:00Z\",\n  },\n  {\n    id: \"brand-2\",\n    name: \"Samsung\",\n    description:\n      \"Global leader in technology, semiconductors, and consumer electronics\",\n    slug: \"samsung\",\n    logo: \"📱\",\n    website: \"https://samsung.com\",\n    email: \"<EMAIL>\",\n    country: \"South Korea\",\n    foundedYear: 1938,\n    isActive: true,\n    isPremium: true,\n    productCount: 156,\n    rating: 4.6,\n    sortOrder: 2,\n    createdAt: \"2024-01-02T00:00:00Z\",\n    updatedAt: \"2024-01-14T16:45:00Z\",\n  },\n  {\n    id: \"brand-3\",\n    name: \"Nike\",\n    description: \"World's leading supplier of athletic shoes and apparel\",\n    slug: \"nike\",\n    logo: \"✅\",\n    website: \"https://nike.com\",\n    email: \"<EMAIL>\",\n    country: \"United States\",\n    foundedYear: 1964,\n    isActive: true,\n    isPremium: true,\n    productCount: 234,\n    rating: 4.7,\n    sortOrder: 3,\n    createdAt: \"2024-01-03T00:00:00Z\",\n    updatedAt: \"2024-01-13T09:20:00Z\",\n  },\n  {\n    id: \"brand-4\",\n    name: \"IKEA\",\n    description:\n      \"Swedish furniture retailer known for affordable, functional home furnishing\",\n    slug: \"ikea\",\n    logo: \"🏠\",\n    website: \"https://ikea.com\",\n    email: \"<EMAIL>\",\n    country: \"Sweden\",\n    foundedYear: 1943,\n    isActive: true,\n    isPremium: false,\n    productCount: 445,\n    rating: 4.3,\n    sortOrder: 4,\n    createdAt: \"2024-01-04T00:00:00Z\",\n    updatedAt: \"2024-01-12T14:15:00Z\",\n  },\n  {\n    id: \"brand-5\",\n    name: \"Adidas\",\n    description:\n      \"German multinational corporation that designs and manufactures sports equipment\",\n    slug: \"adidas\",\n    logo: \"👟\",\n    website: \"https://adidas.com\",\n    email: \"<EMAIL>\",\n    country: \"Germany\",\n    foundedYear: 1949,\n    isActive: true,\n    isPremium: true,\n    productCount: 178,\n    rating: 4.5,\n    sortOrder: 5,\n    createdAt: \"2024-01-05T00:00:00Z\",\n    updatedAt: \"2024-01-11T11:30:00Z\",\n  },\n  {\n    id: \"brand-6\",\n    name: \"Sony\",\n    description:\n      \"Japanese multinational conglomerate focused on electronics and entertainment\",\n    slug: \"sony\",\n    logo: \"🎮\",\n    website: \"https://sony.com\",\n    email: \"<EMAIL>\",\n    country: \"Japan\",\n    foundedYear: 1946,\n    isActive: false,\n    isPremium: true,\n    productCount: 67,\n    rating: 4.4,\n    sortOrder: 6,\n    createdAt: \"2024-01-06T00:00:00Z\",\n    updatedAt: \"2024-01-10T08:45:00Z\",\n  },\n];\n\ntype BrandManagerProps = {\n  initialBrands?: Brand[];\n  onBrandsChange?: (brands: Brand[]) => void;\n};\n\n/**\n * Enhanced component for managing product brands with professional UI\n */\nexport const BrandManagerEnhanced = ({\n  initialBrands = mockBrands,\n  onBrandsChange,\n}: BrandManagerProps) => {\n  const [brands, setBrands] = useState<Brand[]>(initialBrands);\n  const [newBrand, setNewBrand] = useState<Partial<Brand>>({\n    name: \"\",\n    description: \"\",\n    logo: \"\",\n    website: \"\",\n    email: \"\",\n    country: \"\",\n    foundedYear: undefined,\n    isActive: true,\n    isPremium: false,\n  });\n  const [editingBrandId, setEditingBrandId] = useState<string | null>(null);\n  const [editForm, setEditForm] = useState<Partial<Brand>>({});\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [filterStatus, setFilterStatus] = useState(\"all\");\n  const [filterType, setFilterType] = useState(\"all\");\n  const [sortBy, setSortBy] = useState(\"name\");\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"asc\");\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\n  const [showAddForm, setShowAddForm] = useState(false);\n\n  // Generate a slug from the brand name\n  const generateSlug = (name: string) => {\n    return name\n      .toLowerCase()\n      .replace(/[^a-z0-9]+/g, \"-\")\n      .replace(/^-|-$/g, \"\");\n  };\n\n  // Filter and sort brands\n  const filteredAndSortedBrands = brands\n    .filter((brand) => {\n      const matchesSearch =\n        brand.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        brand.description.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        brand.country?.toLowerCase().includes(searchQuery.toLowerCase());\n\n      const matchesStatus =\n        filterStatus === \"all\" ||\n        (filterStatus === \"active\" && brand.isActive) ||\n        (filterStatus === \"inactive\" && !brand.isActive);\n\n      const matchesType =\n        filterType === \"all\" ||\n        (filterType === \"premium\" && brand.isPremium) ||\n        (filterType === \"standard\" && !brand.isPremium);\n\n      return matchesSearch && matchesStatus && matchesType;\n    })\n    .sort((a, b) => {\n      let comparison = 0;\n      switch (sortBy) {\n        case \"name\":\n          comparison = a.name.localeCompare(b.name);\n          break;\n        case \"products\":\n          comparison = a.productCount - b.productCount;\n          break;\n        case \"rating\":\n          comparison = a.rating - b.rating;\n          break;\n        case \"founded\":\n          comparison = (a.foundedYear || 0) - (b.foundedYear || 0);\n          break;\n        case \"created\":\n          comparison =\n            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\n          break;\n        default:\n          comparison = a.sortOrder - b.sortOrder;\n      }\n      return sortOrder === \"asc\" ? comparison : -comparison;\n    });\n\n  // Add a new brand\n  const handleAddBrand = () => {\n    if (!newBrand.name) {\n      toast.error(\"Brand name is required\");\n      return;\n    }\n\n    const slug = generateSlug(newBrand.name);\n\n    // Check if slug already exists\n    if (brands.some((brand) => brand.slug === slug)) {\n      toast.error(\"A brand with this name already exists\");\n      return;\n    }\n\n    const newBrandWithId: Brand = {\n      id: `brand-${Date.now()}`,\n      name: newBrand.name,\n      description: newBrand.description || \"\",\n      slug,\n      logo: newBrand.logo || \"🏢\",\n      website: newBrand.website || \"\",\n      email: newBrand.email || \"\",\n      country: newBrand.country || \"\",\n      foundedYear: newBrand.foundedYear,\n      isActive: newBrand.isActive ?? true,\n      isPremium: newBrand.isPremium ?? false,\n      productCount: 0,\n      rating: 0,\n      sortOrder: brands.length + 1,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n\n    const updatedBrands = [...brands, newBrandWithId];\n    setBrands(updatedBrands);\n    setNewBrand({\n      name: \"\",\n      description: \"\",\n      logo: \"\",\n      website: \"\",\n      email: \"\",\n      country: \"\",\n      foundedYear: undefined,\n      isActive: true,\n      isPremium: false,\n    });\n    setShowAddForm(false);\n\n    // Notify parent component\n    if (onBrandsChange) {\n      onBrandsChange(updatedBrands);\n    }\n\n    toast.success(\"Brand added successfully\");\n  };\n\n  // Start editing a brand\n  const handleEditStart = (brand: Brand) => {\n    setEditingBrandId(brand.id);\n    setEditForm({ ...brand });\n  };\n\n  // Cancel editing\n  const handleEditCancel = () => {\n    setEditingBrandId(null);\n    setEditForm({});\n  };\n\n  // Save edited brand\n  const handleEditSave = () => {\n    if (!editForm.name) {\n      toast.error(\"Brand name is required\");\n      return;\n    }\n\n    const updatedBrands = brands.map((brand) =>\n      brand.id === editingBrandId\n        ? {\n            ...brand,\n            name: editForm.name || brand.name,\n            description: editForm.description || brand.description,\n            logo: editForm.logo || brand.logo,\n            website: editForm.website || brand.website,\n            email: editForm.email || brand.email,\n            country: editForm.country || brand.country,\n            foundedYear: editForm.foundedYear || brand.foundedYear,\n            isActive: editForm.isActive ?? brand.isActive,\n            isPremium: editForm.isPremium ?? brand.isPremium,\n            // Only update slug if name changed\n            slug:\n              brand.name !== editForm.name\n                ? generateSlug(editForm.name)\n                : brand.slug,\n            updatedAt: new Date().toISOString(),\n          }\n        : brand\n    );\n\n    setBrands(updatedBrands);\n    setEditingBrandId(null);\n    setEditForm({});\n\n    // Notify parent component\n    if (onBrandsChange) {\n      onBrandsChange(updatedBrands);\n    }\n\n    toast.success(\"Brand updated successfully\");\n  };\n\n  // Delete a brand\n  const handleDeleteBrand = (brandId: string) => {\n    const updatedBrands = brands.filter((brand) => brand.id !== brandId);\n    setBrands(updatedBrands);\n\n    // Notify parent component\n    if (onBrandsChange) {\n      onBrandsChange(updatedBrands);\n    }\n\n    toast.success(\"Brand deleted successfully\");\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Statistics */}\n      <div className=\"grid gap-4 md:grid-cols-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-green-100 p-2\">\n                <Tag className=\"h-5 w-5 text-green-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Total Brands</p>\n                <p className=\"text-2xl font-bold\">{brands.length}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-blue-100 p-2\">\n                <TrendingUp className=\"h-5 w-5 text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Active Brands</p>\n                <p className=\"text-2xl font-bold\">\n                  {brands.filter((b) => b.isActive).length}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-purple-100 p-2\">\n                <Award className=\"h-5 w-5 text-purple-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Premium Brands</p>\n                <p className=\"text-2xl font-bold\">\n                  {brands.filter((b) => b.isPremium).length}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-orange-100 p-2\">\n                <Star className=\"h-5 w-5 text-orange-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Avg Rating</p>\n                <p className=\"text-2xl font-bold\">\n                  {brands.length > 0\n                    ? (\n                        brands.reduce((sum, b) => sum + b.rating, 0) /\n                        brands.length\n                      ).toFixed(1)\n                    : \"0.0\"}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Controls */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\n            {/* Search and Filters */}\n            <div className=\"flex flex-1 gap-4\">\n              <div className=\"relative max-w-md flex-1\">\n                <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n                <Input\n                  placeholder=\"Search brands...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n\n              <Select value={filterStatus} onValueChange={setFilterStatus}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Status</SelectItem>\n                  <SelectItem value=\"active\">Active</SelectItem>\n                  <SelectItem value=\"inactive\">Inactive</SelectItem>\n                </SelectContent>\n              </Select>\n\n              <Select value={filterType} onValueChange={setFilterType}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Types</SelectItem>\n                  <SelectItem value=\"premium\">Premium</SelectItem>\n                  <SelectItem value=\"standard\">Standard</SelectItem>\n                </SelectContent>\n              </Select>\n\n              <Select value={sortBy} onValueChange={setSortBy}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"name\">Name</SelectItem>\n                  <SelectItem value=\"products\">Products</SelectItem>\n                  <SelectItem value=\"rating\">Rating</SelectItem>\n                  <SelectItem value=\"founded\">Founded</SelectItem>\n                  <SelectItem value=\"created\">Created</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* View Controls */}\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant={sortOrder === \"asc\" ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() =>\n                  setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\")\n                }\n              >\n                {sortOrder === \"asc\" ? (\n                  <SortAsc className=\"h-4 w-4\" />\n                ) : (\n                  <SortDesc className=\"h-4 w-4\" />\n                )}\n              </Button>\n\n              <div className=\"flex rounded-md border\">\n                <Button\n                  variant={viewMode === \"grid\" ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"grid\")}\n                  className=\"rounded-r-none\"\n                >\n                  <Grid3X3 className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  variant={viewMode === \"list\" ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"list\")}\n                  className=\"rounded-l-none\"\n                >\n                  <List className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              <Button onClick={() => setShowAddForm(true)}>\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Add Brand\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Add Brand Form */}\n      {showAddForm && (\n        <Card className=\"border-2 border-green-200 bg-green-50/50\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Plus className=\"h-5 w-5 text-green-600\" />\n              Add New Brand\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid gap-4 md:grid-cols-2\">\n              <div>\n                <Label htmlFor=\"new-brand-name\">Brand Name *</Label>\n                <Input\n                  id=\"new-brand-name\"\n                  value={newBrand.name}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, name: e.target.value })\n                  }\n                  placeholder=\"e.g. Apple\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-brand-logo\">Logo (Emoji)</Label>\n                <Input\n                  id=\"new-brand-logo\"\n                  value={newBrand.logo}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, logo: e.target.value })\n                  }\n                  placeholder=\"🍎\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div className=\"md:col-span-2\">\n                <Label htmlFor=\"new-brand-description\">Description</Label>\n                <Textarea\n                  id=\"new-brand-description\"\n                  value={newBrand.description}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, description: e.target.value })\n                  }\n                  placeholder=\"Describe the brand, its values, and what makes it unique...\"\n                  className=\"mt-1\"\n                  rows={3}\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-brand-website\">Website</Label>\n                <Input\n                  id=\"new-brand-website\"\n                  type=\"url\"\n                  value={newBrand.website}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, website: e.target.value })\n                  }\n                  placeholder=\"https://example.com\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-brand-email\">Contact Email</Label>\n                <Input\n                  id=\"new-brand-email\"\n                  type=\"email\"\n                  value={newBrand.email}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, email: e.target.value })\n                  }\n                  placeholder=\"<EMAIL>\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-brand-country\">Country</Label>\n                <Input\n                  id=\"new-brand-country\"\n                  value={newBrand.country}\n                  onChange={(e) =>\n                    setNewBrand({ ...newBrand, country: e.target.value })\n                  }\n                  placeholder=\"United States\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div>\n                <Label htmlFor=\"new-brand-founded\">Founded Year</Label>\n                <Input\n                  id=\"new-brand-founded\"\n                  type=\"number\"\n                  min=\"1800\"\n                  max={new Date().getFullYear()}\n                  value={newBrand.foundedYear || \"\"}\n                  onChange={(e) =>\n                    setNewBrand({\n                      ...newBrand,\n                      foundedYear: parseInt(e.target.value) || undefined,\n                    })\n                  }\n                  placeholder=\"1976\"\n                  className=\"mt-1\"\n                />\n              </div>\n\n              <div className=\"flex items-center space-x-2 pt-6\">\n                <Switch\n                  id=\"new-brand-active\"\n                  checked={newBrand.isActive}\n                  onCheckedChange={(checked) =>\n                    setNewBrand({ ...newBrand, isActive: checked })\n                  }\n                />\n                <Label htmlFor=\"new-brand-active\">Active brand</Label>\n              </div>\n\n              <div className=\"flex items-center space-x-2 pt-6\">\n                <Switch\n                  id=\"new-brand-premium\"\n                  checked={newBrand.isPremium}\n                  onCheckedChange={(checked) =>\n                    setNewBrand({ ...newBrand, isPremium: checked })\n                  }\n                />\n                <Label htmlFor=\"new-brand-premium\">Premium brand</Label>\n              </div>\n            </div>\n\n            <div className=\"mt-6 flex gap-2\">\n              <Button onClick={handleAddBrand}>\n                <Save className=\"mr-2 h-4 w-4\" />\n                Add Brand\n              </Button>\n              <Button variant=\"outline\" onClick={() => setShowAddForm(false)}>\n                <X className=\"mr-2 h-4 w-4\" />\n                Cancel\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Brands Display */}\n      {viewMode === \"grid\" ? (\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n          {filteredAndSortedBrands.map((brand) => (\n            <Card key={brand.id} className=\"transition-shadow hover:shadow-md\">\n              <CardContent className=\"p-4\">\n                {editingBrandId === brand.id ? (\n                  // Edit form\n                  <div className=\"space-y-3\">\n                    <Input\n                      value={editForm.name || \"\"}\n                      onChange={(e) =>\n                        setEditForm({ ...editForm, name: e.target.value })\n                      }\n                      placeholder=\"Brand name\"\n                    />\n                    <Textarea\n                      value={editForm.description || \"\"}\n                      onChange={(e) =>\n                        setEditForm({\n                          ...editForm,\n                          description: e.target.value,\n                        })\n                      }\n                      placeholder=\"Description\"\n                      rows={2}\n                    />\n                    <div className=\"flex gap-2\">\n                      <Button size=\"sm\" onClick={handleEditSave}>\n                        <Save className=\"mr-1 h-3 w-3\" />\n                        Save\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={handleEditCancel}\n                      >\n                        <X className=\"h-3 w-3\" />\n                      </Button>\n                    </div>\n                  </div>\n                ) : (\n                  // Display mode\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-center gap-3\">\n                        <span className=\"text-3xl\">{brand.logo}</span>\n                        <div>\n                          <h3 className=\"font-semibold\">{brand.name}</h3>\n                          <div className=\"flex gap-1\">\n                            <Badge\n                              variant={brand.isActive ? \"default\" : \"secondary\"}\n                              className=\"text-xs\"\n                            >\n                              {brand.isActive ? \"Active\" : \"Inactive\"}\n                            </Badge>\n                            {brand.isPremium && (\n                              <Badge\n                                variant=\"outline\"\n                                className=\"border-purple-200 text-xs text-purple-600\"\n                              >\n                                Premium\n                              </Badge>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n\n                      <DropdownMenu>\n                        <DropdownMenuTrigger asChild>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <MoreHorizontal className=\"h-4 w-4\" />\n                          </Button>\n                        </DropdownMenuTrigger>\n                        <DropdownMenuContent align=\"end\">\n                          <DropdownMenuItem\n                            onClick={() => handleEditStart(brand)}\n                          >\n                            <Edit className=\"mr-2 h-4 w-4\" />\n                            Edit\n                          </DropdownMenuItem>\n                          <DropdownMenuItem>\n                            <Eye className=\"mr-2 h-4 w-4\" />\n                            View Products\n                          </DropdownMenuItem>\n                          {brand.website && (\n                            <DropdownMenuItem asChild>\n                              <a\n                                href={brand.website}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                              >\n                                <ExternalLink className=\"mr-2 h-4 w-4\" />\n                                Visit Website\n                              </a>\n                            </DropdownMenuItem>\n                          )}\n                          <DropdownMenuItem>\n                            <Copy className=\"mr-2 h-4 w-4\" />\n                            Duplicate\n                          </DropdownMenuItem>\n                          <DropdownMenuSeparator />\n                          <DropdownMenuItem>\n                            <Archive className=\"mr-2 h-4 w-4\" />\n                            Archive\n                          </DropdownMenuItem>\n                          <DropdownMenuItem\n                            onClick={() => handleDeleteBrand(brand.id)}\n                            className=\"text-red-600\"\n                          >\n                            <Trash2 className=\"mr-2 h-4 w-4\" />\n                            Delete\n                          </DropdownMenuItem>\n                        </DropdownMenuContent>\n                      </DropdownMenu>\n                    </div>\n\n                    <p className=\"line-clamp-2 text-sm text-gray-600\">\n                      {brand.description || \"No description provided\"}\n                    </p>\n\n                    <div className=\"space-y-2 text-xs text-gray-500\">\n                      <div className=\"flex items-center justify-between\">\n                        <span>{brand.productCount} products</span>\n                        <div className=\"flex items-center gap-1\">\n                          <Star className=\"h-3 w-3 fill-yellow-400 text-yellow-400\" />\n                          <span>{brand.rating.toFixed(1)}</span>\n                        </div>\n                      </div>\n\n                      {brand.country && (\n                        <div className=\"flex items-center gap-1\">\n                          <Globe className=\"h-3 w-3\" />\n                          <span>{brand.country}</span>\n                          {brand.foundedYear && (\n                            <span>• Est. {brand.foundedYear}</span>\n                          )}\n                        </div>\n                      )}\n\n                      {brand.website && (\n                        <div className=\"flex items-center gap-1\">\n                          <ExternalLink className=\"h-3 w-3\" />\n                          <a\n                            href={brand.website}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"truncate text-blue-600 hover:underline\"\n                          >\n                            {brand.website.replace(/^https?:\\/\\//, \"\")}\n                          </a>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      ) : (\n        // List view - simplified for now\n        <Card>\n          <CardContent className=\"p-0\">\n            <div className=\"divide-y\">\n              {filteredAndSortedBrands.map((brand) => (\n                <div key={brand.id} className=\"p-4 hover:bg-gray-50\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-4\">\n                      <span className=\"text-2xl\">{brand.logo}</span>\n                      <div>\n                        <div className=\"flex items-center gap-2\">\n                          <h3 className=\"font-semibold\">{brand.name}</h3>\n                          <Badge\n                            variant={brand.isActive ? \"default\" : \"secondary\"}\n                            className=\"text-xs\"\n                          >\n                            {brand.isActive ? \"Active\" : \"Inactive\"}\n                          </Badge>\n                          {brand.isPremium && (\n                            <Badge\n                              variant=\"outline\"\n                              className=\"border-purple-200 text-xs text-purple-600\"\n                            >\n                              Premium\n                            </Badge>\n                          )}\n                        </div>\n                        <p className=\"text-sm text-gray-600\">\n                          {brand.description || \"No description\"}\n                        </p>\n                        <div className=\"mt-1 flex items-center gap-4 text-xs text-gray-500\">\n                          <span>{brand.productCount} products</span>\n                          <span>Rating: {brand.rating.toFixed(1)}</span>\n                          {brand.country && <span>{brand.country}</span>}\n                          {brand.foundedYear && (\n                            <span>Est. {brand.foundedYear}</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"flex gap-2\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => handleEditStart(brand)}\n                      >\n                        <Pencil className=\"h-4 w-4\" />\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => handleDeleteBrand(brand.id)}\n                      >\n                        <Trash2 className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Empty State */}\n      {filteredAndSortedBrands.length === 0 && (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <Building2 className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-lg font-medium text-gray-900\">\n              No brands found\n            </h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {searchQuery || filterStatus !== \"all\" || filterType !== \"all\"\n                ? \"Try adjusting your search or filter criteria.\"\n                : \"Get started by adding your first brand.\"}\n            </p>\n            {!searchQuery && filterStatus === \"all\" && filterType === \"all\" && (\n              <Button className=\"mt-4\" onClick={() => setShowAddForm(true)}>\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Add Your First Brand\n              </Button>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AAOA;AACA;AAtDA;;;;;;;;;;;;;;AA2EA,iCAAiC;AACjC,MAAM,aAAsB;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aACE;QACF,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;QACX,cAAc;QACd,QAAQ;QACR,WAAW;QACX,WAAW;QACX,WAAW;IACb;CACD;AAUM,MAAM,uBAAuB,CAAC,EACnC,gBAAgB,UAAU,EAC1B,cAAc,EACI;IAClB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAW;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,MAAM;QACN,aAAa;QACb,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,aAAa;QACb,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,sCAAsC;IACtC,MAAM,eAAe,CAAC;QACpB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,UAAU;IACvB;IAEA,yBAAyB;IACzB,MAAM,0BAA0B,OAC7B,MAAM,CAAC,CAAC;QACP,MAAM,gBACJ,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAChE,MAAM,OAAO,EAAE,cAAc,SAAS,YAAY,WAAW;QAE/D,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,MAAM,QAAQ,IAC3C,iBAAiB,cAAc,CAAC,MAAM,QAAQ;QAEjD,MAAM,cACJ,eAAe,SACd,eAAe,aAAa,MAAM,SAAS,IAC3C,eAAe,cAAc,CAAC,MAAM,SAAS;QAEhD,OAAO,iBAAiB,iBAAiB;IAC3C,GACC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC;YACF,KAAK;gBACH,aAAa,EAAE,YAAY,GAAG,EAAE,YAAY;gBAC5C;YACF,KAAK;gBACH,aAAa,EAAE,MAAM,GAAG,EAAE,MAAM;gBAChC;YACF,KAAK;gBACH,aAAa,CAAC,EAAE,WAAW,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,IAAI,CAAC;gBACvD;YACF,KAAK;gBACH,aACE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACjE;YACF;gBACE,aAAa,EAAE,SAAS,GAAG,EAAE,SAAS;QAC1C;QACA,OAAO,cAAc,QAAQ,aAAa,CAAC;IAC7C;IAEF,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,OAAO,aAAa,SAAS,IAAI;QAEvC,+BAA+B;QAC/B,IAAI,OAAO,IAAI,CAAC,CAAC,QAAU,MAAM,IAAI,KAAK,OAAO;YAC/C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,iBAAwB;YAC5B,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW,IAAI;YACrC;YACA,MAAM,SAAS,IAAI,IAAI;YACvB,SAAS,SAAS,OAAO,IAAI;YAC7B,OAAO,SAAS,KAAK,IAAI;YACzB,SAAS,SAAS,OAAO,IAAI;YAC7B,aAAa,SAAS,WAAW;YACjC,UAAU,SAAS,QAAQ,IAAI;YAC/B,WAAW,SAAS,SAAS,IAAI;YACjC,cAAc;YACd,QAAQ;YACR,WAAW,OAAO,MAAM,GAAG;YAC3B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,gBAAgB;eAAI;YAAQ;SAAe;QACjD,UAAU;QACV,YAAY;YACV,MAAM;YACN,aAAa;YACb,MAAM;YACN,SAAS;YACT,OAAO;YACP,SAAS;YACT,aAAa;YACb,UAAU;YACV,WAAW;QACb;QACA,eAAe;QAEf,0BAA0B;QAC1B,IAAI,gBAAgB;YAClB,eAAe;QACjB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,wBAAwB;IACxB,MAAM,kBAAkB,CAAC;QACvB,kBAAkB,MAAM,EAAE;QAC1B,YAAY;YAAE,GAAG,KAAK;QAAC;IACzB;IAEA,iBAAiB;IACjB,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,YAAY,CAAC;IACf;IAEA,oBAAoB;IACpB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,gBAAgB,OAAO,GAAG,CAAC,CAAC,QAChC,MAAM,EAAE,KAAK,iBACT;gBACE,GAAG,KAAK;gBACR,MAAM,SAAS,IAAI,IAAI,MAAM,IAAI;gBACjC,aAAa,SAAS,WAAW,IAAI,MAAM,WAAW;gBACtD,MAAM,SAAS,IAAI,IAAI,MAAM,IAAI;gBACjC,SAAS,SAAS,OAAO,IAAI,MAAM,OAAO;gBAC1C,OAAO,SAAS,KAAK,IAAI,MAAM,KAAK;gBACpC,SAAS,SAAS,OAAO,IAAI,MAAM,OAAO;gBAC1C,aAAa,SAAS,WAAW,IAAI,MAAM,WAAW;gBACtD,UAAU,SAAS,QAAQ,IAAI,MAAM,QAAQ;gBAC7C,WAAW,SAAS,SAAS,IAAI,MAAM,SAAS;gBAChD,mCAAmC;gBACnC,MACE,MAAM,IAAI,KAAK,SAAS,IAAI,GACxB,aAAa,SAAS,IAAI,IAC1B,MAAM,IAAI;gBAChB,WAAW,IAAI,OAAO,WAAW;YACnC,IACA;QAGN,UAAU;QACV,kBAAkB;QAClB,YAAY,CAAC;QAEb,0BAA0B;QAC1B,IAAI,gBAAgB;YAClB,eAAe;QACjB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,iBAAiB;IACjB,MAAM,oBAAoB,CAAC;QACzB,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;QAC5D,UAAU;QAEV,0BAA0B;QAC1B,IAAI,gBAAgB;YAClB,eAAe;QACjB;QAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,oRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMxD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,wRAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,OAAO,MAAM,GAAG,IACb,CACE,OAAO,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,KAC1C,OAAO,MAAM,AACf,EAAE,OAAO,CAAC,KACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAId,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe;;0DAC1C,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe;;0DACxC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAQ,eAAe;;0DACpC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS,cAAc,QAAQ,YAAY;wCAC3C,MAAK;wCACL,SAAS,IACP,aAAa,cAAc,QAAQ,SAAS;kDAG7C,cAAc,sBACb,6WAAC,kTAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6WAAC,qTAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAIxB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,gSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIpB,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,eAAe;;0DACpC,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,6BACC,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA2B;;;;;;;;;;;;kCAI/C,6WAAC,yHAAA,CAAA,cAAW;;0CACV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAElD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAElD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAwB;;;;;;0DACvC,6WAAC,6HAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAEzD,aAAY;gDACZ,WAAU;gDACV,MAAM;;;;;;;;;;;;kDAIV,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAErD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAEnD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAErD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,KAAK,IAAI,OAAO,WAAW;gDAC3B,OAAO,SAAS,WAAW,IAAI;gDAC/B,UAAU,CAAC,IACT,YAAY;wDACV,GAAG,QAAQ;wDACX,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oDAC3C;gDAEF,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,SAAS,QAAQ;gDAC1B,iBAAiB,CAAC,UAChB,YAAY;wDAAE,GAAG,QAAQ;wDAAE,UAAU;oDAAQ;;;;;;0DAGjD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAmB;;;;;;;;;;;;kDAGpC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,SAAS,SAAS;gDAC3B,iBAAiB,CAAC,UAChB,YAAY;wDAAE,GAAG,QAAQ;wDAAE,WAAW;oDAAQ;;;;;;0DAGlD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;;;;;;;;;;;;;0CAIvC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,eAAe;;0DACtD,6WAAC,gRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YASvC,aAAa,uBACZ,6WAAC;gBAAI,WAAU;0BACZ,wBAAwB,GAAG,CAAC,CAAC,sBAC5B,6WAAC,yHAAA,CAAA,OAAI;wBAAgB,WAAU;kCAC7B,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,mBAAmB,MAAM,EAAE,GAC1B,YAAY;0CACZ,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0HAAA,CAAA,QAAK;wCACJ,OAAO,SAAS,IAAI,IAAI;wCACxB,UAAU,CAAC,IACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAElD,aAAY;;;;;;kDAEd,6WAAC,6HAAA,CAAA,WAAQ;wCACP,OAAO,SAAS,WAAW,IAAI;wCAC/B,UAAU,CAAC,IACT,YAAY;gDACV,GAAG,QAAQ;gDACX,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC7B;wCAEF,aAAY;wCACZ,MAAM;;;;;;kDAER,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAS;;kEACzB,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6WAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;0DAET,cAAA,6WAAC,gRAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;uCAKnB,eAAe;0CACf,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAK,WAAU;kEAAY,MAAM,IAAI;;;;;;kEACtC,6WAAC;;0EACC,6WAAC;gEAAG,WAAU;0EAAiB,MAAM,IAAI;;;;;;0EACzC,6WAAC;gEAAI,WAAU;;kFACb,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAS,MAAM,QAAQ,GAAG,YAAY;wEACtC,WAAU;kFAET,MAAM,QAAQ,GAAG,WAAW;;;;;;oEAE9B,MAAM,SAAS,kBACd,6WAAC,0HAAA,CAAA,QAAK;wEACJ,SAAQ;wEACR,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;0DAQT,6WAAC,qIAAA,CAAA,eAAY;;kEACX,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,gBAAgB;;kFAE/B,6WAAC,+RAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;4DAGjC,MAAM,OAAO,kBACZ,6WAAC,qIAAA,CAAA,mBAAgB;gEAAC,OAAO;0EACvB,cAAA,6WAAC;oEACC,MAAM,MAAM,OAAO;oEACnB,QAAO;oEACP,KAAI;;sFAEJ,6WAAC,0SAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;0EAK/C,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;0EACtB,6WAAC,qIAAA,CAAA,mBAAgB;;kFACf,6WAAC,4RAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGtC,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,kBAAkB,MAAM,EAAE;gEACzC,WAAU;;kFAEV,6WAAC,8RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kDAO3C,6WAAC;wCAAE,WAAU;kDACV,MAAM,WAAW,IAAI;;;;;;kDAGxB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;;4DAAM,MAAM,YAAY;4DAAC;;;;;;;kEAC1B,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6WAAC;0EAAM,MAAM,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;4CAI/B,MAAM,OAAO,kBACZ,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,wRAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6WAAC;kEAAM,MAAM,OAAO;;;;;;oDACnB,MAAM,WAAW,kBAChB,6WAAC;;4DAAK;4DAAQ,MAAM,WAAW;;;;;;;;;;;;;4CAKpC,MAAM,OAAO,kBACZ,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,0SAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,6WAAC;wDACC,MAAM,MAAM,OAAO;wDACnB,QAAO;wDACP,KAAI;wDACJ,WAAU;kEAET,MAAM,OAAO,CAAC,OAAO,CAAC,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAjJ5C,MAAM,EAAE;;;;;;;;;uBA6JvB,iCAAiC;0BACjC,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;kCACZ,wBAAwB,GAAG,CAAC,CAAC,sBAC5B,6WAAC;gCAAmB,WAAU;0CAC5B,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAK,WAAU;8DAAY,MAAM,IAAI;;;;;;8DACtC,6WAAC;;sEACC,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAG,WAAU;8EAAiB,MAAM,IAAI;;;;;;8EACzC,6WAAC,0HAAA,CAAA,QAAK;oEACJ,SAAS,MAAM,QAAQ,GAAG,YAAY;oEACtC,WAAU;8EAET,MAAM,QAAQ,GAAG,WAAW;;;;;;gEAE9B,MAAM,SAAS,kBACd,6WAAC,0HAAA,CAAA,QAAK;oEACJ,SAAQ;oEACR,WAAU;8EACX;;;;;;;;;;;;sEAKL,6WAAC;4DAAE,WAAU;sEACV,MAAM,WAAW,IAAI;;;;;;sEAExB,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;;wEAAM,MAAM,YAAY;wEAAC;;;;;;;8EAC1B,6WAAC;;wEAAK;wEAAS,MAAM,MAAM,CAAC,OAAO,CAAC;;;;;;;gEACnC,MAAM,OAAO,kBAAI,6WAAC;8EAAM,MAAM,OAAO;;;;;;gEACrC,MAAM,WAAW,kBAChB,6WAAC;;wEAAK;wEAAM,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;sDAMrC,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,gBAAgB;8DAE/B,cAAA,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,kBAAkB,MAAM,EAAE;8DAEzC,cAAA,6WAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BAjDhB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;YA6D3B,wBAAwB,MAAM,KAAK,mBAClC,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC,oSAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6WAAC;4BAAE,WAAU;sCACV,eAAe,iBAAiB,SAAS,eAAe,QACrD,kDACA;;;;;;wBAEL,CAAC,eAAe,iBAAiB,SAAS,eAAe,uBACxD,6WAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;4BAAO,SAAS,IAAM,eAAe;;8CACrD,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 2442, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2466, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/colors/ColorManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useState } from \"react\";\n\nimport {\n  Archive,\n  Check,\n  Copy,\n  Edit,\n  Eye,\n  Filter,\n  Grid3X3,\n  List,\n  MoreHorizontal,\n  Package,\n  Palette,\n  Pencil,\n  Plus,\n  Save,\n  Search,\n  SortAsc,\n  SortDesc,\n  Star,\n  Trash2,\n  TrendingUp,\n  X,\n} from \"lucide-react\";\nimport { useRouter } from \"next/navigation\";\nimport { toast } from \"sonner\";\n\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { useColors } from \"@/hooks/useColors\";\nimport { Color, CreateColorDto } from \"@/lib/api/colors\";\n\ninterface ColorManagerEnhancedProps {\n  initialColors?: Color[];\n  onColorsChange?: (colors: Color[]) => void;\n}\n\n/**\n * Enhanced component for managing product colors with real API integration\n */\nexport const ColorManagerEnhanced = ({\n  initialColors = [],\n  onColorsChange,\n}: ColorManagerEnhancedProps) => {\n  const router = useRouter();\n\n  // API hooks\n  const {\n    colors: apiColors,\n    loading,\n    error,\n    createColor,\n    updateColor,\n    deleteColor,\n    refreshColors,\n  } = useColors();\n\n  // Local state\n  const [colors, setColors] = useState<Color[]>(initialColors);\n  const [newColor, setNewColor] = useState<Partial<CreateColorDto>>({\n    name: \"\",\n    description: \"\",\n    hexValue: \"#000000\",\n    colorFamily: \"gray\",\n    isActive: true,\n  });\n  const [editingColorId, setEditingColorId] = useState<string | null>(null);\n  const [editForm, setEditForm] = useState<Partial<Color>>({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [showInactive, setShowInactive] = useState(false);\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\n  const [sortBy, setSortBy] = useState<\"name\" | \"created\" | \"products\">(\"name\");\n  const [filterFamily, setFilterFamily] = useState<string>(\"all\");\n\n  // Helper function to convert hex to RGB\n  const hexToRgb = (hex: string) => {\n    const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n    return result\n      ? {\n          r: parseInt(result[1], 16),\n          g: parseInt(result[2], 16),\n          b: parseInt(result[3], 16),\n        }\n      : { r: 0, g: 0, b: 0 };\n  };\n\n  // Helper function to convert RGB to HSL\n  const rgbToHsl = (r: number, g: number, b: number) => {\n    r /= 255;\n    g /= 255;\n    b /= 255;\n\n    const max = Math.max(r, g, b);\n    const min = Math.min(r, g, b);\n    let h = 0,\n      s = 0,\n      l = (max + min) / 2;\n\n    if (max === min) {\n      h = s = 0; // achromatic\n    } else {\n      const d = max - min;\n      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n\n      switch (max) {\n        case r:\n          h = (g - b) / d + (g < b ? 6 : 0);\n          break;\n        case g:\n          h = (b - r) / d + 2;\n          break;\n        case b:\n          h = (r - g) / d + 4;\n          break;\n      }\n      h /= 6;\n    }\n\n    return {\n      h: Math.round(h * 360),\n      s: Math.round(s * 100),\n      l: Math.round(l * 100),\n    };\n  };\n\n  // Helper function to determine color family from hex\n  const getColorFamily = (hex: string): string => {\n    const rgb = hexToRgb(hex);\n    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);\n\n    // Determine color family based on HSL values\n    if (hsl.s < 10) {\n      if (hsl.l < 20) return \"black\";\n      if (hsl.l > 90) return \"white\";\n      return \"gray\";\n    }\n\n    const hue = hsl.h;\n    if (hue >= 0 && hue < 15) return \"red\";\n    if (hue >= 15 && hue < 45) return \"orange\";\n    if (hue >= 45 && hue < 75) return \"yellow\";\n    if (hue >= 75 && hue < 150) return \"green\";\n    if (hue >= 150 && hue < 250) return \"blue\";\n    if (hue >= 250 && hue < 290) return \"purple\";\n    if (hue >= 290 && hue < 330) return \"pink\";\n    if (hue >= 330 && hue < 360) return \"red\";\n    return \"gray\";\n  };\n\n  // Update local colors when API data changes\n  useEffect(() => {\n    setColors(apiColors);\n    if (onColorsChange) {\n      onColorsChange(apiColors);\n    }\n  }, [apiColors, onColorsChange]);\n\n  // Filter and sort colors\n  const filteredColors = colors\n    .filter((color) => {\n      const matchesSearch = color.name\n        .toLowerCase()\n        .includes(searchTerm.toLowerCase());\n      const matchesStatus = showInactive || color.isActive;\n      const matchesFamily =\n        filterFamily === \"all\" || color.colorFamily === filterFamily;\n      return matchesSearch && matchesStatus && matchesFamily;\n    })\n    .sort((a, b) => {\n      switch (sortBy) {\n        case \"name\":\n          return a.name.localeCompare(b.name);\n        case \"created\":\n          return (\n            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n          );\n        case \"products\":\n          return (b.productCount || 0) - (a.productCount || 0);\n        default:\n          return 0;\n      }\n    });\n\n  // Handle create color\n  const handleCreateColor = async () => {\n    if (!newColor.name?.trim()) {\n      toast.error(\"Color name is required\");\n      return;\n    }\n\n    if (!newColor.hexValue || !/^#[0-9A-F]{6}$/i.test(newColor.hexValue)) {\n      toast.error(\"Valid hex color code is required (e.g., #FF0000)\");\n      return;\n    }\n\n    try {\n      // Generate RGB and HSL values from hex\n      const rgb = hexToRgb(newColor.hexValue);\n      const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);\n\n      // Auto-detect color family if not set\n      const colorFamily =\n        newColor.colorFamily || getColorFamily(newColor.hexValue);\n\n      const colorData: CreateColorDto = {\n        name: newColor.name,\n        description: newColor.description || \"\",\n        hexValue: newColor.hexValue,\n        rgbValue: rgb,\n        hslValue: hsl,\n        colorFamily: colorFamily as any,\n        isActive: newColor.isActive ?? true,\n      };\n\n      await createColor(colorData);\n      setNewColor({\n        name: \"\",\n        description: \"\",\n        hexValue: \"#000000\",\n        colorFamily: \"gray\",\n        isActive: true,\n      });\n      setShowAddForm(false);\n      refreshColors();\n    } catch (error) {\n      console.error(\"Error creating color:\", error);\n    }\n  };\n\n  // Handle update color\n  const handleUpdateColor = async () => {\n    if (!editingColorId || !editForm.name?.trim()) {\n      toast.error(\"Color name is required\");\n      return;\n    }\n\n    try {\n      await updateColor(editingColorId, editForm);\n      setEditingColorId(null);\n      setEditForm({});\n      refreshColors();\n    } catch (error) {\n      console.error(\"Error updating color:\", error);\n    }\n  };\n\n  // Handle delete color\n  const handleDeleteColor = async (colorId: string) => {\n    if (!confirm(\"Are you sure you want to delete this color?\")) {\n      return;\n    }\n\n    try {\n      await deleteColor(colorId);\n      refreshColors();\n    } catch (error) {\n      console.error(\"Error deleting color:\", error);\n    }\n  };\n\n  // Statistics\n  const stats = {\n    total: colors.length,\n    active: colors.filter((c) => c.isActive).length,\n    primary: colors.filter((c) => c.family === \"primary\").length,\n    neutral: colors.filter((c) => c.family === \"neutral\").length,\n  };\n\n  if (error) {\n    return (\n      <div className=\"rounded-lg border border-red-200 bg-red-50 p-4\">\n        <p className=\"text-red-600\">Error loading colors: {error}</p>\n        <Button onClick={refreshColors} className=\"mt-2\">\n          Retry\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Statistics */}\n      <div className=\"grid gap-4 md:grid-cols-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-pink-100 p-2\">\n                <Palette className=\"h-5 w-5 text-pink-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Total Colors</p>\n                <p className=\"text-2xl font-bold\">{stats.total}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-green-100 p-2\">\n                <TrendingUp className=\"h-5 w-5 text-green-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Active Colors</p>\n                <p className=\"text-2xl font-bold\">{stats.active}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-blue-100 p-2\">\n                <Star className=\"h-5 w-5 text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Primary</p>\n                <p className=\"text-2xl font-bold\">{stats.primary}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-gray-100 p-2\">\n                <Package className=\"h-5 w-5 text-gray-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Neutral</p>\n                <p className=\"text-2xl font-bold\">{stats.neutral}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Controls */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\n            {/* Search and Filters */}\n            <div className=\"flex flex-1 gap-4\">\n              <div className=\"relative max-w-md flex-1\">\n                <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n                <Input\n                  placeholder=\"Search colors...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n\n              <Select value={filterFamily} onValueChange={setFilterFamily}>\n                <SelectTrigger className=\"w-32\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Families</SelectItem>\n                  <SelectItem value=\"primary\">Primary</SelectItem>\n                  <SelectItem value=\"secondary\">Secondary</SelectItem>\n                  <SelectItem value=\"neutral\">Neutral</SelectItem>\n                  <SelectItem value=\"warm\">Warm</SelectItem>\n                  <SelectItem value=\"cool\">Cool</SelectItem>\n                </SelectContent>\n              </Select>\n\n              <Select\n                value={sortBy}\n                onValueChange={(value: any) => setSortBy(value)}\n              >\n                <SelectTrigger className=\"w-40\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"name\">Name</SelectItem>\n                  <SelectItem value=\"created\">Created</SelectItem>\n                  <SelectItem value=\"products\">Products</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* Actions */}\n            <div className=\"flex gap-2\">\n              <div className=\"flex items-center gap-2\">\n                <Switch\n                  checked={showInactive}\n                  onCheckedChange={setShowInactive}\n                />\n                <Label className=\"text-sm\">Show inactive</Label>\n              </div>\n\n              <div className=\"flex gap-1\">\n                <Button\n                  variant={viewMode === \"grid\" ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"grid\")}\n                >\n                  <Grid3X3 className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  variant={viewMode === \"list\" ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"list\")}\n                >\n                  <List className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              <Button onClick={() => setShowAddForm(true)}>\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Add Color\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Add Color Form */}\n      {showAddForm && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Add New Color</CardTitle>\n            <CardDescription>\n              Create a new color for your product catalog\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid gap-4 md:grid-cols-2\">\n              <div>\n                <Label htmlFor=\"color-name\">Name *</Label>\n                <Input\n                  id=\"color-name\"\n                  value={newColor.name || \"\"}\n                  onChange={(e) =>\n                    setNewColor({ ...newColor, name: e.target.value })\n                  }\n                  placeholder=\"e.g., Ocean Blue\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"color-hex\">Color Selection *</Label>\n                <div className=\"space-y-3\">\n                  {/* Color Picker */}\n                  <div className=\"flex gap-2\">\n                    <input\n                      type=\"color\"\n                      id=\"color-picker\"\n                      value={newColor.hexValue || \"#000000\"}\n                      onChange={(e) =>\n                        setNewColor({\n                          ...newColor,\n                          hexValue: e.target.value,\n                          colorFamily: getColorFamily(e.target.value) as any,\n                        })\n                      }\n                      className=\"h-10 w-16 cursor-pointer rounded border\"\n                    />\n                    <Input\n                      id=\"color-hex\"\n                      value={newColor.hexValue || \"\"}\n                      onChange={(e) =>\n                        setNewColor({\n                          ...newColor,\n                          hexValue: e.target.value,\n                          colorFamily: getColorFamily(e.target.value) as any,\n                        })\n                      }\n                      placeholder=\"#FF0000\"\n                      className=\"flex-1\"\n                    />\n                  </div>\n\n                  {/* Preset Colors */}\n                  <div>\n                    <Label className=\"text-xs text-gray-500\">\n                      Quick Colors\n                    </Label>\n                    <div className=\"mt-1 flex gap-2\">\n                      {[\n                        \"#FF0000\",\n                        \"#00FF00\",\n                        \"#0000FF\",\n                        \"#FFFF00\",\n                        \"#FF00FF\",\n                        \"#00FFFF\",\n                        \"#FFA500\",\n                        \"#800080\",\n                        \"#FFC0CB\",\n                        \"#A52A2A\",\n                        \"#808080\",\n                        \"#000000\",\n                      ].map((color) => (\n                        <button\n                          key={color}\n                          type=\"button\"\n                          className=\"h-8 w-8 rounded border-2 border-gray-300 transition-colors hover:border-gray-500\"\n                          style={{ backgroundColor: color }}\n                          onClick={() =>\n                            setNewColor({\n                              ...newColor,\n                              hexValue: color,\n                              colorFamily: getColorFamily(color) as any,\n                            })\n                          }\n                          title={color}\n                        />\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"grid gap-4 md:grid-cols-2\">\n              <div>\n                <Label htmlFor=\"color-family\">Family</Label>\n                <Select\n                  value={newColor.colorFamily}\n                  onValueChange={(value: any) =>\n                    setNewColor({ ...newColor, colorFamily: value })\n                  }\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"red\">Red</SelectItem>\n                    <SelectItem value=\"orange\">Orange</SelectItem>\n                    <SelectItem value=\"yellow\">Yellow</SelectItem>\n                    <SelectItem value=\"green\">Green</SelectItem>\n                    <SelectItem value=\"blue\">Blue</SelectItem>\n                    <SelectItem value=\"purple\">Purple</SelectItem>\n                    <SelectItem value=\"pink\">Pink</SelectItem>\n                    <SelectItem value=\"brown\">Brown</SelectItem>\n                    <SelectItem value=\"gray\">Gray</SelectItem>\n                    <SelectItem value=\"black\">Black</SelectItem>\n                    <SelectItem value=\"white\">White</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n\n            <div>\n              <Label htmlFor=\"color-description\">Description</Label>\n              <Textarea\n                id=\"color-description\"\n                value={newColor.description || \"\"}\n                onChange={(e) =>\n                  setNewColor({ ...newColor, description: e.target.value })\n                }\n                placeholder=\"Describe the color and its uses...\"\n                rows={3}\n              />\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <Switch\n                checked={newColor.isActive}\n                onCheckedChange={(checked) =>\n                  setNewColor({ ...newColor, isActive: checked })\n                }\n              />\n              <Label>Active</Label>\n            </div>\n\n            <div className=\"flex gap-2 pt-2\">\n              <Button onClick={handleCreateColor}>\n                <Check className=\"mr-2 h-4 w-4\" />\n                Add Color\n              </Button>\n              <Button variant=\"outline\" onClick={() => setShowAddForm(false)}>\n                <X className=\"mr-2 h-4 w-4\" />\n                Cancel\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Colors Display */}\n      {loading ? (\n        <div className=\"space-y-2\">\n          {[...Array(5)].map((_, i) => (\n            <Skeleton key={i} className=\"h-20 w-full\" />\n          ))}\n        </div>\n      ) : filteredColors.length === 0 ? (\n        <Card>\n          <CardContent className=\"py-8 text-center\">\n            <p className=\"text-muted-foreground\">No colors found</p>\n            {searchTerm && (\n              <Button\n                variant=\"link\"\n                onClick={() => setSearchTerm(\"\")}\n                className=\"mt-2\"\n              >\n                Clear search\n              </Button>\n            )}\n          </CardContent>\n        </Card>\n      ) : (\n        <div\n          className={\n            viewMode === \"grid\"\n              ? \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\"\n              : \"space-y-3\"\n          }\n        >\n          {filteredColors.map((color) => (\n            <Card\n              key={color._id}\n              className={`transition-shadow hover:shadow-md ${\n                !color.isActive ? \"opacity-60\" : \"\"\n              }`}\n            >\n              <CardContent className=\"p-4\">\n                {editingColorId === color._id ? (\n                  // Edit form\n                  <div className=\"space-y-3\">\n                    <Input\n                      value={editForm.name || \"\"}\n                      onChange={(e) =>\n                        setEditForm({ ...editForm, name: e.target.value })\n                      }\n                      placeholder=\"Color name\"\n                    />\n                    <div className=\"space-y-2\">\n                      <div className=\"flex gap-2\">\n                        <input\n                          type=\"color\"\n                          value={editForm.hexValue || \"#000000\"}\n                          onChange={(e) =>\n                            setEditForm({\n                              ...editForm,\n                              hexValue: e.target.value,\n                            })\n                          }\n                          className=\"h-10 w-16 cursor-pointer rounded border\"\n                        />\n                        <Input\n                          value={editForm.hexValue || \"\"}\n                          onChange={(e) =>\n                            setEditForm({\n                              ...editForm,\n                              hexValue: e.target.value,\n                            })\n                          }\n                          placeholder=\"#FF0000\"\n                          className=\"flex-1\"\n                        />\n                      </div>\n                      <div className=\"flex gap-1\">\n                        {[\n                          \"#FF0000\",\n                          \"#00FF00\",\n                          \"#0000FF\",\n                          \"#FFFF00\",\n                          \"#FF00FF\",\n                          \"#00FFFF\",\n                        ].map((color) => (\n                          <button\n                            key={color}\n                            type=\"button\"\n                            className=\"h-6 w-6 rounded border border-gray-300 transition-colors hover:border-gray-500\"\n                            style={{ backgroundColor: color }}\n                            onClick={() =>\n                              setEditForm({ ...editForm, hexValue: color })\n                            }\n                            title={color}\n                          />\n                        ))}\n                      </div>\n                    </div>\n                    <Textarea\n                      value={editForm.description || \"\"}\n                      onChange={(e) =>\n                        setEditForm({\n                          ...editForm,\n                          description: e.target.value,\n                        })\n                      }\n                      placeholder=\"Description\"\n                      rows={2}\n                    />\n                    <div className=\"flex gap-2\">\n                      <Button size=\"sm\" onClick={handleUpdateColor}>\n                        <Save className=\"mr-2 h-4 w-4\" />\n                        Save\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => {\n                          setEditingColorId(null);\n                          setEditForm({});\n                        }}\n                      >\n                        <X className=\"mr-2 h-4 w-4\" />\n                        Cancel\n                      </Button>\n                    </div>\n                  </div>\n                ) : (\n                  // Display mode\n                  <div>\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-center gap-3\">\n                        <div\n                          className=\"h-12 w-12 rounded-lg border-2 border-gray-200 shadow-sm\"\n                          style={{ backgroundColor: color.hexValue }}\n                        />\n                        <div>\n                          <h3 className=\"font-semibold text-gray-900\">\n                            {color.name}\n                          </h3>\n                          <p className=\"text-sm text-gray-600\">\n                            {color.hexValue}\n                          </p>\n                          <div className=\"mt-1 flex items-center gap-2\">\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {color.colorFamily}\n                            </Badge>\n                            {!color.isActive && (\n                              <Badge variant=\"secondary\" className=\"text-xs\">\n                                Inactive\n                              </Badge>\n                            )}\n                            <span className=\"text-xs text-gray-500\">\n                              {color.productCount || 0} products\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <DropdownMenu>\n                        <DropdownMenuTrigger asChild>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <MoreHorizontal className=\"h-4 w-4\" />\n                          </Button>\n                        </DropdownMenuTrigger>\n                        <DropdownMenuContent align=\"end\">\n                          <DropdownMenuItem\n                            onClick={() => {\n                              setEditingColorId(color._id);\n                              setEditForm(color);\n                            }}\n                          >\n                            <Edit className=\"mr-2 h-4 w-4\" />\n                            Edit\n                          </DropdownMenuItem>\n                          <DropdownMenuItem>\n                            <Eye className=\"mr-2 h-4 w-4\" />\n                            View Products\n                          </DropdownMenuItem>\n                          <DropdownMenuItem>\n                            <Copy className=\"mr-2 h-4 w-4\" />\n                            Duplicate\n                          </DropdownMenuItem>\n                          <DropdownMenuSeparator />\n                          <DropdownMenuItem\n                            onClick={() => handleDeleteColor(color._id)}\n                            className=\"text-red-600\"\n                          >\n                            <Trash2 className=\"mr-2 h-4 w-4\" />\n                            Delete\n                          </DropdownMenuItem>\n                        </DropdownMenuContent>\n                      </DropdownMenu>\n                    </div>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AACA;AAEA;AACA;AACA;AAOA;AAOA;AACA;AACA;AAOA;AACA;AACA;AACA;AA1DA;;;;;;;;;;;;;;;;;AAqEO,MAAM,uBAAuB,CAAC,EACnC,gBAAgB,EAAE,EAClB,cAAc,EACY;IAC1B,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,YAAY;IACZ,MAAM,EACJ,QAAQ,SAAS,EACjB,OAAO,EACP,KAAK,EACL,WAAW,EACX,WAAW,EACX,WAAW,EACX,aAAa,EACd,GAAG,CAAA,GAAA,kHAAA,CAAA,YAAS,AAAD;IAEZ,cAAc;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAW;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA2B;QAChE,MAAM;QACN,aAAa;QACb,UAAU;QACV,aAAa;QACb,UAAU;IACZ;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmC;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,wCAAwC;IACxC,MAAM,WAAW,CAAC;QAChB,MAAM,SAAS,4CAA4C,IAAI,CAAC;QAChE,OAAO,SACH;YACE,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;YACvB,GAAG,SAAS,MAAM,CAAC,EAAE,EAAE;QACzB,IACA;YAAE,GAAG;YAAG,GAAG;YAAG,GAAG;QAAE;IACzB;IAEA,wCAAwC;IACxC,MAAM,WAAW,CAAC,GAAW,GAAW;QACtC,KAAK;QACL,KAAK;QACL,KAAK;QAEL,MAAM,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG;QAC3B,MAAM,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG;QAC3B,IAAI,IAAI,GACN,IAAI,GACJ,IAAI,CAAC,MAAM,GAAG,IAAI;QAEpB,IAAI,QAAQ,KAAK;YACf,IAAI,IAAI,GAAG,aAAa;QAC1B,OAAO;YACL,MAAM,IAAI,MAAM;YAChB,IAAI,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG;YAElD,OAAQ;gBACN,KAAK;oBACH,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;oBAChC;gBACF,KAAK;oBACH,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;oBAClB;gBACF,KAAK;oBACH,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;oBAClB;YACJ;YACA,KAAK;QACP;QAEA,OAAO;YACL,GAAG,KAAK,KAAK,CAAC,IAAI;YAClB,GAAG,KAAK,KAAK,CAAC,IAAI;YAClB,GAAG,KAAK,KAAK,CAAC,IAAI;QACpB;IACF;IAEA,qDAAqD;IACrD,MAAM,iBAAiB,CAAC;QACtB,MAAM,MAAM,SAAS;QACrB,MAAM,MAAM,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;QAExC,6CAA6C;QAC7C,IAAI,IAAI,CAAC,GAAG,IAAI;YACd,IAAI,IAAI,CAAC,GAAG,IAAI,OAAO;YACvB,IAAI,IAAI,CAAC,GAAG,IAAI,OAAO;YACvB,OAAO;QACT;QAEA,MAAM,MAAM,IAAI,CAAC;QACjB,IAAI,OAAO,KAAK,MAAM,IAAI,OAAO;QACjC,IAAI,OAAO,MAAM,MAAM,IAAI,OAAO;QAClC,IAAI,OAAO,MAAM,MAAM,IAAI,OAAO;QAClC,IAAI,OAAO,MAAM,MAAM,KAAK,OAAO;QACnC,IAAI,OAAO,OAAO,MAAM,KAAK,OAAO;QACpC,IAAI,OAAO,OAAO,MAAM,KAAK,OAAO;QACpC,IAAI,OAAO,OAAO,MAAM,KAAK,OAAO;QACpC,IAAI,OAAO,OAAO,MAAM,KAAK,OAAO;QACpC,OAAO;IACT;IAEA,4CAA4C;IAC5C,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU;QACV,IAAI,gBAAgB;YAClB,eAAe;QACjB;IACF,GAAG;QAAC;QAAW;KAAe;IAE9B,yBAAyB;IACzB,MAAM,iBAAiB,OACpB,MAAM,CAAC,CAAC;QACP,MAAM,gBAAgB,MAAM,IAAI,CAC7B,WAAW,GACX,QAAQ,CAAC,WAAW,WAAW;QAClC,MAAM,gBAAgB,gBAAgB,MAAM,QAAQ;QACpD,MAAM,gBACJ,iBAAiB,SAAS,MAAM,WAAW,KAAK;QAClD,OAAO,iBAAiB,iBAAiB;IAC3C,GACC,IAAI,CAAC,CAAC,GAAG;QACR,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,OACE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YAEnE,KAAK;gBACH,OAAO,CAAC,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC;YACrD;gBACE,OAAO;QACX;IACF;IAEF,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,IAAI,CAAC,SAAS,IAAI,EAAE,QAAQ;YAC1B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,kBAAkB,IAAI,CAAC,SAAS,QAAQ,GAAG;YACpE,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,uCAAuC;YACvC,MAAM,MAAM,SAAS,SAAS,QAAQ;YACtC,MAAM,MAAM,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;YAExC,sCAAsC;YACtC,MAAM,cACJ,SAAS,WAAW,IAAI,eAAe,SAAS,QAAQ;YAE1D,MAAM,YAA4B;gBAChC,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW,IAAI;gBACrC,UAAU,SAAS,QAAQ;gBAC3B,UAAU;gBACV,UAAU;gBACV,aAAa;gBACb,UAAU,SAAS,QAAQ,IAAI;YACjC;YAEA,MAAM,YAAY;YAClB,YAAY;gBACV,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,aAAa;gBACb,UAAU;YACZ;YACA,eAAe;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,IAAI,CAAC,kBAAkB,CAAC,SAAS,IAAI,EAAE,QAAQ;YAC7C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,YAAY,gBAAgB;YAClC,kBAAkB;YAClB,YAAY,CAAC;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,gDAAgD;YAC3D;QACF;QAEA,IAAI;YACF,MAAM,YAAY;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,aAAa;IACb,MAAM,QAAQ;QACZ,OAAO,OAAO,MAAM;QACpB,QAAQ,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;QAC/C,SAAS,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,WAAW,MAAM;QAC5D,SAAS,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,WAAW,MAAM;IAC9D;IAEA,IAAI,OAAO;QACT,qBACE,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAE,WAAU;;wBAAe;wBAAuB;;;;;;;8BACnD,6WAAC,2HAAA,CAAA,SAAM;oBAAC,SAAS;oBAAe,WAAU;8BAAO;;;;;;;;;;;;IAKvD;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMtD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMvD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMxD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1D,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAId,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe;;0DAC1C,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;;;;;;;;;;;;;kDAI7B,6WAAC,2HAAA,CAAA,SAAM;wCACL,OAAO;wCACP,eAAe,CAAC,QAAe,UAAU;;0DAEzC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;;;;;;;0CAMnC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS;gDACT,iBAAiB;;;;;;0DAEnB,6WAAC,0HAAA,CAAA,QAAK;gDAAC,WAAU;0DAAU;;;;;;;;;;;;kDAG7B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;0DAE3B,cAAA,6WAAC,gSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;0DAE3B,cAAA,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIpB,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,eAAe;;0DACpC,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,6BACC,6WAAC,yHAAA,CAAA,OAAI;;kCACH,6WAAC,yHAAA,CAAA,aAAU;;0CACT,6WAAC,yHAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6WAAC,yHAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6WAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,IAAI,IAAI;gDACxB,UAAU,CAAC,IACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAElD,aAAY;;;;;;;;;;;;kDAGhB,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,6WAAC;gDAAI,WAAU;;kEAEb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEACC,MAAK;gEACL,IAAG;gEACH,OAAO,SAAS,QAAQ,IAAI;gEAC5B,UAAU,CAAC,IACT,YAAY;wEACV,GAAG,QAAQ;wEACX,UAAU,EAAE,MAAM,CAAC,KAAK;wEACxB,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;oEAC5C;gEAEF,WAAU;;;;;;0EAEZ,6WAAC,0HAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,SAAS,QAAQ,IAAI;gEAC5B,UAAU,CAAC,IACT,YAAY;wEACV,GAAG,QAAQ;wEACX,UAAU,EAAE,MAAM,CAAC,KAAK;wEACxB,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;oEAC5C;gEAEF,aAAY;gEACZ,WAAU;;;;;;;;;;;;kEAKd,6WAAC;;0EACC,6WAAC,0HAAA,CAAA,QAAK;gEAAC,WAAU;0EAAwB;;;;;;0EAGzC,6WAAC;gEAAI,WAAU;0EACZ;oEACC;oEACA;oEACA;oEACA;oEACA;oEACA;oEACA;oEACA;oEACA;oEACA;oEACA;oEACA;iEACD,CAAC,GAAG,CAAC,CAAC,sBACL,6WAAC;wEAEC,MAAK;wEACL,WAAU;wEACV,OAAO;4EAAE,iBAAiB;wEAAM;wEAChC,SAAS,IACP,YAAY;gFACV,GAAG,QAAQ;gFACX,UAAU;gFACV,aAAa,eAAe;4EAC9B;wEAEF,OAAO;uEAXF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAoBnB,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;;sDACC,6WAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAe;;;;;;sDAC9B,6WAAC,2HAAA,CAAA,SAAM;4CACL,OAAO,SAAS,WAAW;4CAC3B,eAAe,CAAC,QACd,YAAY;oDAAE,GAAG,QAAQ;oDAAE,aAAa;gDAAM;;8DAGhD,6WAAC,2HAAA,CAAA,gBAAa;8DACZ,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;8DAEd,6WAAC,2HAAA,CAAA,gBAAa;;sEACZ,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;sEAC1B,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;sEAC1B,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;sEAC1B,6WAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,6WAAC;;kDACC,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAoB;;;;;;kDACnC,6WAAC,6HAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,SAAS,WAAW,IAAI;wCAC/B,UAAU,CAAC,IACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAEzD,aAAY;wCACZ,MAAM;;;;;;;;;;;;0CAIV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS,SAAS,QAAQ;wCAC1B,iBAAiB,CAAC,UAChB,YAAY;gDAAE,GAAG,QAAQ;gDAAE,UAAU;4CAAQ;;;;;;kDAGjD,6WAAC,0HAAA,CAAA,QAAK;kDAAC;;;;;;;;;;;;0CAGT,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,6WAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,eAAe;;0DACtD,6WAAC,gRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YASvC,wBACC,6WAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6WAAC,6HAAA,CAAA,WAAQ;wBAAS,WAAU;uBAAb;;;;;;;;;uBAGjB,eAAe,MAAM,KAAK,kBAC5B,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC;4BAAE,WAAU;sCAAwB;;;;;;wBACpC,4BACC,6WAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,cAAc;4BAC7B,WAAU;sCACX;;;;;;;;;;;;;;;;qCAOP,6WAAC;gBACC,WACE,aAAa,SACT,6CACA;0BAGL,eAAe,GAAG,CAAC,CAAC,sBACnB,6WAAC,yHAAA,CAAA,OAAI;wBAEH,WAAW,CAAC,kCAAkC,EAC5C,CAAC,MAAM,QAAQ,GAAG,eAAe,IACjC;kCAEF,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,mBAAmB,MAAM,GAAG,GAC3B,YAAY;0CACZ,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0HAAA,CAAA,QAAK;wCACJ,OAAO,SAAS,IAAI,IAAI;wCACxB,UAAU,CAAC,IACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAElD,aAAY;;;;;;kDAEd,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDACC,MAAK;wDACL,OAAO,SAAS,QAAQ,IAAI;wDAC5B,UAAU,CAAC,IACT,YAAY;gEACV,GAAG,QAAQ;gEACX,UAAU,EAAE,MAAM,CAAC,KAAK;4DAC1B;wDAEF,WAAU;;;;;;kEAEZ,6WAAC,0HAAA,CAAA,QAAK;wDACJ,OAAO,SAAS,QAAQ,IAAI;wDAC5B,UAAU,CAAC,IACT,YAAY;gEACV,GAAG,QAAQ;gEACX,UAAU,EAAE,MAAM,CAAC,KAAK;4DAC1B;wDAEF,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,6WAAC;gDAAI,WAAU;0DACZ;oDACC;oDACA;oDACA;oDACA;oDACA;oDACA;iDACD,CAAC,GAAG,CAAC,CAAC,sBACL,6WAAC;wDAEC,MAAK;wDACL,WAAU;wDACV,OAAO;4DAAE,iBAAiB;wDAAM;wDAChC,SAAS,IACP,YAAY;gEAAE,GAAG,QAAQ;gEAAE,UAAU;4DAAM;wDAE7C,OAAO;uDAPF;;;;;;;;;;;;;;;;kDAYb,6WAAC,6HAAA,CAAA,WAAQ;wCACP,OAAO,SAAS,WAAW,IAAI;wCAC/B,UAAU,CAAC,IACT,YAAY;gDACV,GAAG,QAAQ;gDACX,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC7B;wCAEF,aAAY;wCACZ,MAAM;;;;;;kDAER,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAS;;kEACzB,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6WAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;oDACP,kBAAkB;oDAClB,YAAY,CAAC;gDACf;;kEAEA,6WAAC,gRAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;uCAMpC,eAAe;0CACf,6WAAC;0CACC,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,MAAM,QAAQ;oDAAC;;;;;;8DAE3C,6WAAC;;sEACC,6WAAC;4DAAG,WAAU;sEACX,MAAM,IAAI;;;;;;sEAEb,6WAAC;4DAAE,WAAU;sEACV,MAAM,QAAQ;;;;;;sEAEjB,6WAAC;4DAAI,WAAU;;8EACb,6WAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;8EAChC,MAAM,WAAW;;;;;;gEAEnB,CAAC,MAAM,QAAQ,kBACd,6WAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAAU;;;;;;8EAIjD,6WAAC;oEAAK,WAAU;;wEACb,MAAM,YAAY,IAAI;wEAAE;;;;;;;;;;;;;;;;;;;;;;;;;sDAMjC,6WAAC,qIAAA,CAAA,eAAY;;8DACX,6WAAC,qIAAA,CAAA,sBAAmB;oDAAC,OAAO;8DAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAG9B,6WAAC,qIAAA,CAAA,sBAAmB;oDAAC,OAAM;;sEACzB,6WAAC,qIAAA,CAAA,mBAAgB;4DACf,SAAS;gEACP,kBAAkB,MAAM,GAAG;gEAC3B,YAAY;4DACd;;8EAEA,6WAAC,+RAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,6WAAC,qIAAA,CAAA,mBAAgB;;8EACf,6WAAC,oRAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGlC,6WAAC,qIAAA,CAAA,mBAAgB;;8EACf,6WAAC,sRAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;sEACtB,6WAAC,qIAAA,CAAA,mBAAgB;4DACf,SAAS,IAAM,kBAAkB,MAAM,GAAG;4DAC1C,WAAU;;8EAEV,6WAAC,8RAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAzJ5C,MAAM,GAAG;;;;;;;;;;;;;;;;AAwK5B", "debugId": null}}, {"offset": {"line": 4156, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Popover = PopoverPrimitive.Root\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger\r\n\r\nconst PopoverAnchor = PopoverPrimitive.Anchor\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      ref={ref}\r\n      align={align}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n))\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,6QAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,6QAAA,CAAA,UAAwB;AAE/C,MAAM,gBAAgB,6QAAA,CAAA,SAAuB;AAE7C,MAAM,+BAAiB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,6WAAC,6QAAA,CAAA,SAAuB;kBACtB,cAAA,6WAAC,6QAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,geACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,6QAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4199, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,iRAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6WAAC,iRAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6WAAC;;;;;0BACD,6WAAC,iRAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,iRAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6WAAC,iRAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,iRAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,iRAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4267, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/emoji-selector.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\n\nimport { ChevronDown, Search } from \"lucide-react\";\n\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { cn } from \"@/lib/utils\";\n\n// Emoji database with search functionality\ntype EmojiData = {\n  emoji: string;\n  name: string;\n  keywords: string[];\n  category: string;\n};\n\nconst emojiDatabase: EmojiData[] = [\n  // Shopping & Commerce\n  {\n    emoji: \"🛒\",\n    name: \"shopping cart\",\n    keywords: [\"cart\", \"shopping\", \"buy\"],\n    category: \"Shopping\",\n  },\n  {\n    emoji: \"🛍️\",\n    name: \"shopping bags\",\n    keywords: [\"bags\", \"shopping\", \"retail\"],\n    category: \"Shopping\",\n  },\n  {\n    emoji: \"💳\",\n    name: \"credit card\",\n    keywords: [\"card\", \"payment\", \"money\"],\n    category: \"Shopping\",\n  },\n  {\n    emoji: \"💰\",\n    name: \"money bag\",\n    keywords: [\"money\", \"cash\", \"wealth\"],\n    category: \"Shopping\",\n  },\n  {\n    emoji: \"🏪\",\n    name: \"store\",\n    keywords: [\"store\", \"shop\", \"retail\"],\n    category: \"Shopping\",\n  },\n  {\n    emoji: \"🏬\",\n    name: \"department store\",\n    keywords: [\"mall\", \"shopping\", \"retail\"],\n    category: \"Shopping\",\n  },\n\n  // Electronics & Tech\n  {\n    emoji: \"📱\",\n    name: \"mobile phone\",\n    keywords: [\"phone\", \"mobile\", \"smartphone\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"💻\",\n    name: \"laptop\",\n    keywords: [\"laptop\", \"computer\", \"tech\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"⌚\",\n    name: \"watch\",\n    keywords: [\"watch\", \"time\", \"smartwatch\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"📺\",\n    name: \"television\",\n    keywords: [\"tv\", \"television\", \"screen\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"🎮\",\n    name: \"video game\",\n    keywords: [\"game\", \"gaming\", \"console\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"📷\",\n    name: \"camera\",\n    keywords: [\"camera\", \"photo\", \"photography\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"🎧\",\n    name: \"headphones\",\n    keywords: [\"headphones\", \"audio\", \"music\"],\n    category: \"Electronics\",\n  },\n  {\n    emoji: \"⚡\",\n    name: \"lightning\",\n    keywords: [\"power\", \"energy\", \"electric\"],\n    category: \"Electronics\",\n  },\n\n  // Fashion & Clothing\n  {\n    emoji: \"👕\",\n    name: \"t-shirt\",\n    keywords: [\"shirt\", \"clothing\", \"apparel\"],\n    category: \"Fashion\",\n  },\n  {\n    emoji: \"👔\",\n    name: \"necktie\",\n    keywords: [\"tie\", \"formal\", \"business\"],\n    category: \"Fashion\",\n  },\n  {\n    emoji: \"👗\",\n    name: \"dress\",\n    keywords: [\"dress\", \"clothing\", \"fashion\"],\n    category: \"Fashion\",\n  },\n  {\n    emoji: \"👠\",\n    name: \"high heel\",\n    keywords: [\"shoes\", \"heels\", \"fashion\"],\n    category: \"Fashion\",\n  },\n  {\n    emoji: \"👜\",\n    name: \"handbag\",\n    keywords: [\"bag\", \"purse\", \"fashion\"],\n    category: \"Fashion\",\n  },\n  {\n    emoji: \"🧥\",\n    name: \"coat\",\n    keywords: [\"coat\", \"jacket\", \"outerwear\"],\n    category: \"Fashion\",\n  },\n\n  // Home & Garden\n  {\n    emoji: \"🏠\",\n    name: \"house\",\n    keywords: [\"house\", \"home\", \"building\"],\n    category: \"Home\",\n  },\n  {\n    emoji: \"🛏️\",\n    name: \"bed\",\n    keywords: [\"bed\", \"sleep\", \"bedroom\"],\n    category: \"Home\",\n  },\n  {\n    emoji: \"🪑\",\n    name: \"chair\",\n    keywords: [\"chair\", \"seat\", \"furniture\"],\n    category: \"Home\",\n  },\n  {\n    emoji: \"🛋️\",\n    name: \"couch\",\n    keywords: [\"couch\", \"sofa\", \"furniture\"],\n    category: \"Home\",\n  },\n  {\n    emoji: \"🌱\",\n    name: \"seedling\",\n    keywords: [\"plant\", \"garden\", \"grow\"],\n    category: \"Home\",\n  },\n  {\n    emoji: \"🌸\",\n    name: \"cherry blossom\",\n    keywords: [\"flower\", \"blossom\", \"spring\"],\n    category: \"Home\",\n  },\n\n  // Food & Beverages\n  {\n    emoji: \"🍎\",\n    name: \"apple\",\n    keywords: [\"apple\", \"fruit\", \"food\"],\n    category: \"Food\",\n  },\n  {\n    emoji: \"🍕\",\n    name: \"pizza\",\n    keywords: [\"pizza\", \"food\", \"italian\"],\n    category: \"Food\",\n  },\n  {\n    emoji: \"🍔\",\n    name: \"hamburger\",\n    keywords: [\"burger\", \"hamburger\", \"food\"],\n    category: \"Food\",\n  },\n  {\n    emoji: \"☕\",\n    name: \"coffee\",\n    keywords: [\"coffee\", \"drink\", \"caffeine\"],\n    category: \"Food\",\n  },\n  {\n    emoji: \"🍷\",\n    name: \"wine\",\n    keywords: [\"wine\", \"alcohol\", \"drink\"],\n    category: \"Food\",\n  },\n  {\n    emoji: \"🧀\",\n    name: \"cheese\",\n    keywords: [\"cheese\", \"dairy\", \"food\"],\n    category: \"Food\",\n  },\n\n  // Sports & Fitness\n  {\n    emoji: \"⚽\",\n    name: \"soccer ball\",\n    keywords: [\"soccer\", \"football\", \"sports\"],\n    category: \"Sports\",\n  },\n  {\n    emoji: \"🏀\",\n    name: \"basketball\",\n    keywords: [\"basketball\", \"sports\", \"ball\"],\n    category: \"Sports\",\n  },\n  {\n    emoji: \"🎾\",\n    name: \"tennis\",\n    keywords: [\"tennis\", \"sports\", \"ball\"],\n    category: \"Sports\",\n  },\n  {\n    emoji: \"🏋️\",\n    name: \"weight lifting\",\n    keywords: [\"gym\", \"fitness\", \"workout\"],\n    category: \"Sports\",\n  },\n  {\n    emoji: \"🚴\",\n    name: \"cycling\",\n    keywords: [\"bike\", \"cycling\", \"exercise\"],\n    category: \"Sports\",\n  },\n  {\n    emoji: \"🏃\",\n    name: \"running\",\n    keywords: [\"running\", \"exercise\", \"fitness\"],\n    category: \"Sports\",\n  },\n\n  // General\n  {\n    emoji: \"📦\",\n    name: \"package\",\n    keywords: [\"box\", \"package\", \"delivery\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"🏷️\",\n    name: \"label\",\n    keywords: [\"tag\", \"label\", \"price\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"📋\",\n    name: \"clipboard\",\n    keywords: [\"list\", \"clipboard\", \"notes\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"📊\",\n    name: \"bar chart\",\n    keywords: [\"chart\", \"graph\", \"data\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"🎯\",\n    name: \"target\",\n    keywords: [\"target\", \"goal\", \"aim\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"🔥\",\n    name: \"fire\",\n    keywords: [\"fire\", \"hot\", \"trending\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"⭐\",\n    name: \"star\",\n    keywords: [\"star\", \"favorite\", \"rating\"],\n    category: \"General\",\n  },\n  {\n    emoji: \"🎁\",\n    name: \"gift\",\n    keywords: [\"gift\", \"present\", \"surprise\"],\n    category: \"General\",\n  },\n];\n\n// Extract categories for tabs\nconst categories = [\n  \"All\",\n  ...Array.from(new Set(emojiDatabase.map((item) => item.category))),\n];\n\ntype EmojiPickerProps = {\n  value?: string;\n  onChange: (emoji: string) => void;\n  placeholder?: string;\n};\n\nexport const EmojiPicker = ({\n  value,\n  onChange,\n  placeholder = \"Pick an emoji\",\n}: EmojiPickerProps) => {\n  const [open, setOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [selectedCategory, setSelectedCategory] = useState<string>(\"All\");\n\n  // Filter emojis based on category and search\n  const filteredEmojis =\n    selectedCategory === \"All\"\n      ? emojiDatabase\n      : emojiDatabase.filter((item) => item.category === selectedCategory);\n\n  const searchFilteredEmojis = searchQuery\n    ? filteredEmojis.filter((item) => {\n        const query = searchQuery.toLowerCase();\n        return (\n          item.name.toLowerCase().includes(query) ||\n          item.keywords.some((keyword) =>\n            keyword.toLowerCase().includes(query)\n          ) ||\n          item.emoji.includes(query)\n        );\n      })\n    : filteredEmojis;\n\n  const handleEmojiSelect = (emoji: string) => {\n    onChange(emoji);\n    setOpen(false);\n    setSearchQuery(\"\"); // Reset search when emoji is selected\n  };\n\n  return (\n    <div className=\"space-y-2\">\n      <Label>Icon</Label>\n      <Popover open={open} onOpenChange={setOpen}>\n        <PopoverTrigger asChild>\n          <button\n            className={cn(\n              // Base input styles - exactly matching Input component\n              \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background\",\n              \"file:border-0 file:bg-transparent file:text-sm file:font-medium\",\n              \"placeholder:text-muted-foreground\",\n              \"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n              \"disabled:cursor-not-allowed disabled:opacity-50\",\n              // Hover and focus states\n              \"hover:bg-accent hover:text-accent-foreground\",\n              \"transition-colors\",\n              // Open state\n              open && \"ring-2 ring-ring ring-offset-2\"\n            )}\n            type=\"button\"\n            role=\"combobox\"\n            aria-expanded={open}\n            aria-haspopup=\"listbox\"\n            aria-controls=\"emoji-listbox\"\n          >\n            {value ? (\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-2xl duration-200 animate-in fade-in\">\n                  {value}\n                </span>\n                <span className=\"font-medium text-foreground\">Selected</span>\n              </div>\n            ) : (\n              <span className=\"text-muted-foreground\">{placeholder}</span>\n            )}\n            <ChevronDown\n              className={cn(\n                \"h-4 w-4 opacity-50 transition-transform\",\n                open && \"rotate-180\"\n              )}\n            />\n          </button>\n        </PopoverTrigger>\n\n        <PopoverContent className=\"w-80 p-0\" align=\"start\">\n          <div className=\"space-y-4 p-4\">\n            {/* Search */}\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search emojis...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n\n            {/* Category Tabs */}\n            <div className=\"flex flex-wrap gap-1\">\n              {categories.map((category) => (\n                <Button\n                  key={category}\n                  variant={\n                    selectedCategory === category ? \"default\" : \"outline\"\n                  }\n                  size=\"sm\"\n                  onClick={() => setSelectedCategory(category)}\n                >\n                  {category}\n                </Button>\n              ))}\n            </div>\n\n            {/* Emoji Grid */}\n            <ScrollArea className=\"h-64\">\n              <div\n                className=\"grid grid-cols-8 gap-2 p-2\"\n                id=\"emoji-listbox\"\n                role=\"listbox\"\n              >\n                {searchFilteredEmojis.map((item, index) => (\n                  <Button\n                    key={`${item.emoji}-${index}`}\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"h-10 w-10 p-0 hover:bg-muted\"\n                    onClick={() => handleEmojiSelect(item.emoji)}\n                    role=\"option\"\n                    aria-selected={value === item.emoji}\n                    title={item.name}\n                  >\n                    <span className=\"text-xl\">{item.emoji}</span>\n                  </Button>\n                ))}\n              </div>\n              {searchFilteredEmojis.length === 0 && (\n                <div className=\"p-4 text-center text-sm text-muted-foreground\">\n                  No emojis found for \"{searchQuery}\"\n                </div>\n              )}\n            </ScrollArea>\n\n            {/* Custom Input */}\n            <div className=\"border-t pt-4\">\n              <Label className=\"text-sm text-muted-foreground\">\n                Or enter custom emoji:\n              </Label>\n              <Input\n                placeholder=\"🎯\"\n                value={value || \"\"}\n                onChange={(e) => onChange(e.target.value)}\n                className=\"mt-1\"\n                maxLength={4}\n              />\n            </div>\n          </div>\n        </PopoverContent>\n      </Popover>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAEA;AACA;AACA;AACA;AAKA;AACA;AAfA;;;;;;;;;;AAyBA,MAAM,gBAA6B;IACjC,sBAAsB;IACtB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAY;SAAM;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAY;SAAS;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAW;SAAQ;QACtC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAS;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAS;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAY;SAAS;QACxC,UAAU;IACZ;IAEA,qBAAqB;IACrB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAU;SAAa;QAC3C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAY;SAAO;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAa;QACzC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAM;YAAc;SAAS;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAU;SAAU;QACvC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAS;SAAc;QAC5C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAc;YAAS;SAAQ;QAC1C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAU;SAAW;QACzC,UAAU;IACZ;IAEA,qBAAqB;IACrB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAY;SAAU;QAC1C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAU;SAAW;QACvC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAY;SAAU;QAC1C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAS;SAAU;QACvC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAS;SAAU;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAU;SAAY;QACzC,UAAU;IACZ;IAEA,gBAAgB;IAChB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAW;QACvC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAS;SAAU;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAY;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAY;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAU;SAAO;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAW;SAAS;QACzC,UAAU;IACZ;IAEA,mBAAmB;IACnB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAS;SAAO;QACpC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAQ;SAAU;QACtC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAa;SAAO;QACzC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAS;SAAW;QACzC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAW;SAAQ;QACtC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAS;SAAO;QACrC,UAAU;IACZ;IAEA,mBAAmB;IACnB;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAY;SAAS;QAC1C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAc;YAAU;SAAO;QAC1C,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAU;SAAO;QACtC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAW;SAAU;QACvC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAW;SAAW;QACzC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAW;YAAY;SAAU;QAC5C,UAAU;IACZ;IAEA,UAAU;IACV;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAW;SAAW;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAO;YAAS;SAAQ;QACnC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAa;SAAQ;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAS;YAAS;SAAO;QACpC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAU;YAAQ;SAAM;QACnC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAO;SAAW;QACrC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAY;SAAS;QACxC,UAAU;IACZ;IACA;QACE,OAAO;QACP,MAAM;QACN,UAAU;YAAC;YAAQ;YAAW;SAAW;QACzC,UAAU;IACZ;CACD;AAED,8BAA8B;AAC9B,MAAM,aAAa;IACjB;OACG,MAAM,IAAI,CAAC,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC,OAAS,KAAK,QAAQ;CAChE;AAQM,MAAM,cAAc,CAAC,EAC1B,KAAK,EACL,QAAQ,EACR,cAAc,eAAe,EACZ;IACjB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,6CAA6C;IAC7C,MAAM,iBACJ,qBAAqB,QACjB,gBACA,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK;IAEvD,MAAM,uBAAuB,cACzB,eAAe,MAAM,CAAC,CAAC;QACrB,MAAM,QAAQ,YAAY,WAAW;QACrC,OACE,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UACjC,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,UAClB,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAEjC,KAAK,KAAK,CAAC,QAAQ,CAAC;IAExB,KACA;IAEJ,MAAM,oBAAoB,CAAC;QACzB,SAAS;QACT,QAAQ;QACR,eAAe,KAAK,sCAAsC;IAC5D;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,0HAAA,CAAA,QAAK;0BAAC;;;;;;0BACP,6WAAC,4HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc;;kCACjC,6WAAC,4HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6WAAC;4BACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uDAAuD;4BACvD,uIACA,mEACA,qCACA,uGACA,mDACA,yBAAyB;4BACzB,gDACA,qBACA,aAAa;4BACb,QAAQ;4BAEV,MAAK;4BACL,MAAK;4BACL,iBAAe;4BACf,iBAAc;4BACd,iBAAc;;gCAEb,sBACC,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAK,WAAU;sDACb;;;;;;sDAEH,6WAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;yDAGhD,6WAAC;oCAAK,WAAU;8CAAyB;;;;;;8CAE3C,6WAAC,wSAAA,CAAA,cAAW;oCACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2CACA,QAAQ;;;;;;;;;;;;;;;;;kCAMhB,6WAAC,4HAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;kCACzC,cAAA,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6WAAC,0HAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;8CAKd,6WAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6WAAC,2HAAA,CAAA,SAAM;4CAEL,SACE,qBAAqB,WAAW,YAAY;4CAE9C,MAAK;4CACL,SAAS,IAAM,oBAAoB;sDAElC;2CAPI;;;;;;;;;;8CAaX,6WAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6WAAC;4CACC,WAAU;4CACV,IAAG;4CACH,MAAK;sDAEJ,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,6WAAC,2HAAA,CAAA,SAAM;oDAEL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,kBAAkB,KAAK,KAAK;oDAC3C,MAAK;oDACL,iBAAe,UAAU,KAAK,KAAK;oDACnC,OAAO,KAAK,IAAI;8DAEhB,cAAA,6WAAC;wDAAK,WAAU;kEAAW,KAAK,KAAK;;;;;;mDAThC,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;wCAalC,qBAAqB,MAAM,KAAK,mBAC/B,6WAAC;4CAAI,WAAU;;gDAAgD;gDACvC;gDAAY;;;;;;;;;;;;;8CAMxC,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAgC;;;;;;sDAGjD,6WAAC,0HAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO,SAAS;4CAChB,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;4CACV,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3B", "debugId": null}}, {"offset": {"line": 5014, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/management/CategoryManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\n\r\nimport {\r\n  Archive,\r\n  Copy,\r\n  Edit,\r\n  Eye,\r\n  Filter,\r\n  FolderOpen,\r\n  Grid3X3,\r\n  LayoutGrid,\r\n  List,\r\n  MoreHorizontal,\r\n  Package,\r\n  Pencil,\r\n  Plus,\r\n  RefreshCw,\r\n  Save,\r\n  Search,\r\n  SortAsc,\r\n  SortDesc,\r\n  Sparkles,\r\n  Tag,\r\n  Trash2,\r\n  TrendingUp,\r\n  X,\r\n} from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { EmojiPicker } from \"@/components/ui/emoji-selector\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { useCategories, useCategoryMutations } from \"@/hooks/useCategories\";\r\nimport { CategoryApiService } from \"@/lib/api/categoryApi\";\r\n\r\nexport type Category = {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  slug: string;\r\n  icon?: string;\r\n  color?: string;\r\n  isActive: boolean;\r\n  productCount: number;\r\n  parentId?: string;\r\n  sortOrder: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n};\r\n\r\ntype CategoryManagerProps = {\r\n  initialCategories?: Category[];\r\n  onCategoriesChange?: (categories: Category[]) => void;\r\n};\r\n\r\n/**\r\n * Enhanced component for managing product categories with professional UI\r\n */\r\nexport const CategoryManagerEnhanced = ({\r\n  initialCategories = [],\r\n  onCategoriesChange,\r\n}: CategoryManagerProps) => {\r\n  const router = useRouter();\r\n\r\n  // API hooks\r\n  const {\r\n    categories: apiCategories,\r\n    loading,\r\n    error,\r\n    refetch,\r\n  } = useCategories();\r\n  const {\r\n    createCategory,\r\n    updateCategory,\r\n    deleteCategory,\r\n    loading: mutationLoading,\r\n  } = useCategoryMutations();\r\n\r\n  // Local state\r\n  const [categories, setCategories] = useState<Category[]>(initialCategories);\r\n  const [newCategory, setNewCategory] = useState<Partial<Category>>({\r\n    name: \"\",\r\n    description: \"\",\r\n    icon: \"\",\r\n    color: \"#3B82F6\",\r\n    isActive: true,\r\n  });\r\n  const [editingCategoryId, setEditingCategoryId] = useState<string | null>(\r\n    null\r\n  );\r\n  const [editForm, setEditForm] = useState<Partial<Category>>({});\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"all\");\r\n  const [sortBy, setSortBy] = useState(\"name\");\r\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"asc\");\r\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\r\n  const [showAddForm, setShowAddForm] = useState(false);\r\n\r\n  // Update local categories when API data changes\r\n  useEffect(() => {\r\n    if (apiCategories.length > 0) {\r\n      setCategories(apiCategories);\r\n      if (onCategoriesChange) {\r\n        onCategoriesChange(apiCategories);\r\n      }\r\n    } else if (initialCategories.length > 0) {\r\n      setCategories(initialCategories);\r\n    }\r\n  }, [apiCategories, initialCategories, onCategoriesChange]);\r\n\r\n  // Generate a slug from the category name\r\n  const generateSlug = (name: string) => {\r\n    return name\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, \"-\")\r\n      .replace(/^-|-$/g, \"\");\r\n  };\r\n\r\n  // Filter and sort categories\r\n  const filteredAndSortedCategories = categories\r\n    .filter((category) => {\r\n      const matchesSearch =\r\n        category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        category.description.toLowerCase().includes(searchQuery.toLowerCase());\r\n      const matchesFilter =\r\n        filterStatus === \"all\" ||\r\n        (filterStatus === \"active\" && category.isActive) ||\r\n        (filterStatus === \"inactive\" && !category.isActive);\r\n      return matchesSearch && matchesFilter;\r\n    })\r\n    .sort((a, b) => {\r\n      let comparison = 0;\r\n      switch (sortBy) {\r\n        case \"name\":\r\n          comparison = a.name.localeCompare(b.name);\r\n          break;\r\n        case \"products\":\r\n          comparison = a.productCount - b.productCount;\r\n          break;\r\n        case \"created\":\r\n          comparison =\r\n            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();\r\n          break;\r\n        default:\r\n          comparison = a.sortOrder - b.sortOrder;\r\n      }\r\n      return sortOrder === \"asc\" ? comparison : -comparison;\r\n    });\r\n\r\n  // Add a new category\r\n  const handleAddCategory = async () => {\r\n    if (!newCategory.name) {\r\n      toast.error(\"Category name is required\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await createCategory({\r\n        name: newCategory.name,\r\n        description: newCategory.description || \"\",\r\n        icon: newCategory.icon || \"📦\",\r\n        color: newCategory.color || \"#3B82F6\",\r\n        isActive: newCategory.isActive ?? true,\r\n      });\r\n\r\n      // Reset form\r\n      setNewCategory({\r\n        name: \"\",\r\n        description: \"\",\r\n        icon: \"\",\r\n        color: \"#3B82F6\",\r\n        isActive: true,\r\n      });\r\n      setShowAddForm(false);\r\n\r\n      // Refresh categories list\r\n      await refetch();\r\n    } catch (error) {\r\n      // Error is already handled in the hook\r\n      console.error(\"Failed to create category:\", error);\r\n    }\r\n  };\r\n\r\n  // Start editing a category\r\n  const handleEditStart = (category: Category) => {\r\n    setEditingCategoryId(category.id);\r\n    setEditForm({ ...category });\r\n  };\r\n\r\n  // Cancel editing\r\n  const handleEditCancel = () => {\r\n    setEditingCategoryId(null);\r\n    setEditForm({});\r\n  };\r\n\r\n  // Save edited category\r\n  const handleEditSave = async () => {\r\n    if (!editForm.name || !editingCategoryId) {\r\n      toast.error(\"Category name is required\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await updateCategory(editingCategoryId, {\r\n        name: editForm.name,\r\n        description: editForm.description,\r\n        icon: editForm.icon,\r\n        color: editForm.color,\r\n        isActive: editForm.isActive,\r\n      });\r\n\r\n      // Reset editing state\r\n      setEditingCategoryId(null);\r\n      setEditForm({});\r\n\r\n      // Refresh categories list\r\n      await refetch();\r\n    } catch (error) {\r\n      // Error is already handled in the hook\r\n      console.error(\"Failed to update category:\", error);\r\n    }\r\n  };\r\n\r\n  // Delete a category\r\n  const handleDeleteCategory = async (categoryId: string) => {\r\n    try {\r\n      console.log(\"Attempting to delete category with ID:\", categoryId); // Debug log\r\n      console.log(\"Type of categoryId:\", typeof categoryId); // Debug log\r\n\r\n      if (!categoryId) {\r\n        console.error(\"Category ID is undefined or empty\");\r\n        return;\r\n      }\r\n\r\n      await deleteCategory(categoryId);\r\n\r\n      // Refresh categories list\r\n      await refetch();\r\n    } catch (error) {\r\n      // Error is already handled in the hook\r\n      console.error(\"Failed to delete category:\", error);\r\n    }\r\n  };\r\n\r\n  // Recalculate product counts for all categories\r\n  const handleRecalculateProductCounts = async () => {\r\n    try {\r\n      await CategoryApiService.recalculateProductCounts();\r\n      toast.success(\"Product counts recalculated successfully\");\r\n\r\n      // Refresh categories list to show updated counts\r\n      await refetch();\r\n    } catch (error) {\r\n      console.error(\"Failed to recalculate product counts:\", error);\r\n      toast.error(\"Failed to recalculate product counts\");\r\n    }\r\n  };\r\n\r\n  // View products in a category\r\n  const handleViewProducts = (category: Category) => {\r\n    // Navigate to products page with category filter\r\n    router.push(\r\n      `/admin/products?category=${encodeURIComponent(category.name)}`\r\n    );\r\n    toast.success(`Viewing products in \"${category.name}\" category`);\r\n  };\r\n\r\n  // Duplicate a category\r\n  const handleDuplicateCategory = async (category: Category) => {\r\n    try {\r\n      const duplicatedName = `${category.name} (Copy)`;\r\n\r\n      await createCategory({\r\n        name: duplicatedName,\r\n        description: category.description || \"\",\r\n        icon: category.icon || \"📦\",\r\n        color: category.color || \"#3B82F6\",\r\n        isActive: category.isActive,\r\n      });\r\n\r\n      toast.success(`Category \"${duplicatedName}\" created successfully`);\r\n\r\n      // Refresh categories list\r\n      await refetch();\r\n    } catch (error) {\r\n      console.error(\"Failed to duplicate category:\", error);\r\n      toast.error(\"Failed to duplicate category\");\r\n    }\r\n  };\r\n\r\n  // Archive/Unarchive a category (toggle isActive status)\r\n  const handleArchiveCategory = async (category: Category) => {\r\n    try {\r\n      const newStatus = !category.isActive;\r\n      const action = newStatus ? \"unarchived\" : \"archived\";\r\n\r\n      await updateCategory(category.id, {\r\n        isActive: newStatus,\r\n      });\r\n\r\n      toast.success(`Category \"${category.name}\" ${action} successfully`);\r\n\r\n      // Refresh categories list\r\n      await refetch();\r\n    } catch (error) {\r\n      console.error(\"Failed to archive/unarchive category:\", error);\r\n      toast.error(\"Failed to update category status\");\r\n    }\r\n  };\r\n\r\n  // Show loading state\r\n  if (loading && categories.length === 0) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <Card>\r\n          <CardContent className=\"p-12 text-center\">\r\n            <div className=\"mx-auto h-12 w-12 animate-spin rounded-full border-4 border-blue-500 border-t-transparent\"></div>\r\n            <h3 className=\"mt-4 text-lg font-medium text-gray-900\">\r\n              Loading categories...\r\n            </h3>\r\n            <p className=\"mt-1 text-sm text-gray-500\">\r\n              Please wait while we fetch your categories.\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state\r\n  if (error && categories.length === 0) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <Card>\r\n          <CardContent className=\"p-12 text-center\">\r\n            <div className=\"mx-auto h-12 w-12 rounded-full bg-red-100 p-3\">\r\n              <X className=\"h-6 w-6 text-red-600\" />\r\n            </div>\r\n            <h3 className=\"mt-4 text-lg font-medium text-gray-900\">\r\n              Failed to load categories\r\n            </h3>\r\n            <p className=\"mt-1 text-sm text-gray-500\">{error}</p>\r\n            <Button className=\"mt-4\" onClick={() => refetch()}>\r\n              Try Again\r\n            </Button>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header with Statistics */}\r\n      <div className=\"grid gap-4 md:grid-cols-4\">\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-blue-100 p-2\">\r\n                <LayoutGrid className=\"h-5 w-5 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Total Categories</p>\r\n                <p className=\"text-2xl font-bold\">{categories.length}</p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-green-100 p-2\">\r\n                <TrendingUp className=\"h-5 w-5 text-green-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Active Categories</p>\r\n                <p className=\"text-2xl font-bold\">\r\n                  {categories.filter((c) => c.isActive).length}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-orange-100 p-2\">\r\n                <Package className=\"h-5 w-5 text-orange-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Total Products</p>\r\n                <p className=\"text-2xl font-bold\">\r\n                  {categories.reduce((sum, c) => sum + c.productCount, 0)}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardContent className=\"p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-purple-100 p-2\">\r\n                <Sparkles className=\"h-5 w-5 text-purple-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm text-gray-600\">Avg Products</p>\r\n                <p className=\"text-2xl font-bold\">\r\n                  {categories.length > 0\r\n                    ? Math.round(\r\n                        categories.reduce((sum, c) => sum + c.productCount, 0) /\r\n                          categories.length\r\n                      )\r\n                    : 0}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Controls */}\r\n      <Card>\r\n        <CardContent className=\"p-6\">\r\n          <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\r\n            {/* Search and Filters */}\r\n            <div className=\"flex flex-1 gap-4\">\r\n              <div className=\"relative max-w-md flex-1\">\r\n                <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\r\n                <Input\r\n                  placeholder=\"Search categories...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"pl-10\"\r\n                />\r\n              </div>\r\n\r\n              <Select value={filterStatus} onValueChange={setFilterStatus}>\r\n                <SelectTrigger className=\"w-32\">\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All Status</SelectItem>\r\n                  <SelectItem value=\"active\">Active</SelectItem>\r\n                  <SelectItem value=\"inactive\">Inactive</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n\r\n              <Select value={sortBy} onValueChange={setSortBy}>\r\n                <SelectTrigger className=\"w-32\">\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"name\">Name</SelectItem>\r\n                  <SelectItem value=\"products\">Products</SelectItem>\r\n                  <SelectItem value=\"created\">Created</SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            {/* View Controls */}\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                variant={sortOrder === \"asc\" ? \"default\" : \"outline\"}\r\n                size=\"sm\"\r\n                onClick={() =>\r\n                  setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\")\r\n                }\r\n              >\r\n                {sortOrder === \"asc\" ? (\r\n                  <SortAsc className=\"h-4 w-4\" />\r\n                ) : (\r\n                  <SortDesc className=\"h-4 w-4\" />\r\n                )}\r\n              </Button>\r\n\r\n              <div className=\"flex rounded-md border\">\r\n                <Button\r\n                  variant={viewMode === \"grid\" ? \"default\" : \"ghost\"}\r\n                  size=\"sm\"\r\n                  onClick={() => setViewMode(\"grid\")}\r\n                  className=\"rounded-r-none\"\r\n                >\r\n                  <Grid3X3 className=\"h-4 w-4\" />\r\n                </Button>\r\n                <Button\r\n                  variant={viewMode === \"list\" ? \"default\" : \"ghost\"}\r\n                  size=\"sm\"\r\n                  onClick={() => setViewMode(\"list\")}\r\n                  className=\"rounded-l-none\"\r\n                >\r\n                  <List className=\"h-4 w-4\" />\r\n                </Button>\r\n              </div>\r\n\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={handleRecalculateProductCounts}\r\n                disabled={mutationLoading || loading}\r\n              >\r\n                <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n                Fix Counts\r\n              </Button>\r\n\r\n              <Button onClick={() => setShowAddForm(true)}>\r\n                <Plus className=\"mr-2 h-4 w-4\" />\r\n                Add Category\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Add Category Modal */}\r\n      {showAddForm && (\r\n        <Card className=\"border-2 border-blue-200 bg-blue-50/50\">\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <Plus className=\"h-5 w-5 text-blue-600\" />\r\n              Add New Category\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"grid gap-4 md:grid-cols-2\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"new-category-name\">Category Name *</Label>\r\n                <Input\r\n                  id=\"new-category-name\"\r\n                  value={newCategory.name}\r\n                  onChange={(e) =>\r\n                    setNewCategory({ ...newCategory, name: e.target.value })\r\n                  }\r\n                  placeholder=\"e.g. Electronics\"\r\n                  className=\"h-10 bg-background px-3 py-2\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <EmojiPicker\r\n                  value={newCategory.icon}\r\n                  onChange={(emoji) =>\r\n                    setNewCategory({ ...newCategory, icon: emoji })\r\n                  }\r\n                  placeholder=\"Pick an icon for this category\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"md:col-span-2\">\r\n                <Label htmlFor=\"new-category-description\">Description</Label>\r\n                <Textarea\r\n                  id=\"new-category-description\"\r\n                  value={newCategory.description}\r\n                  onChange={(e) =>\r\n                    setNewCategory({\r\n                      ...newCategory,\r\n                      description: e.target.value,\r\n                    })\r\n                  }\r\n                  placeholder=\"Describe what products belong in this category...\"\r\n                  className=\"mt-1 bg-background\"\r\n                  rows={3}\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <Label htmlFor=\"new-category-color\">Color</Label>\r\n                <Input\r\n                  id=\"new-category-color\"\r\n                  type=\"color\"\r\n                  value={newCategory.color}\r\n                  onChange={(e) =>\r\n                    setNewCategory({ ...newCategory, color: e.target.value })\r\n                  }\r\n                  className=\"mt-1 h-10\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"flex items-center space-x-2 pt-6\">\r\n                <Switch\r\n                  id=\"new-category-active\"\r\n                  checked={newCategory.isActive}\r\n                  onCheckedChange={(checked) =>\r\n                    setNewCategory({ ...newCategory, isActive: checked })\r\n                  }\r\n                />\r\n                <Label htmlFor=\"new-category-active\">Active category</Label>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mt-6 flex gap-2\">\r\n              <Button\r\n                onClick={handleAddCategory}\r\n                disabled={mutationLoading || loading}\r\n              >\r\n                <Save className=\"mr-2 h-4 w-4\" />\r\n                {mutationLoading ? \"Adding...\" : \"Add Category\"}\r\n              </Button>\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={() => setShowAddForm(false)}\r\n                disabled={mutationLoading}\r\n              >\r\n                <X className=\"mr-2 h-4 w-4\" />\r\n                Cancel\r\n              </Button>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n\r\n      {/* Categories Display */}\r\n      {viewMode === \"grid\" ? (\r\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\r\n          {filteredAndSortedCategories.map((category) => {\r\n            console.log(\"Rendering category:\", category); // Debug log\r\n            return (\r\n              <Card\r\n                key={category.id}\r\n                className=\"transition-shadow hover:shadow-md\"\r\n              >\r\n                <CardContent className=\"p-4\">\r\n                  {editingCategoryId === category.id ? (\r\n                    // Edit form\r\n                    <div className=\"space-y-3\">\r\n                      <Input\r\n                        value={editForm.name || \"\"}\r\n                        onChange={(e) =>\r\n                          setEditForm({ ...editForm, name: e.target.value })\r\n                        }\r\n                        placeholder=\"Category name\"\r\n                      />\r\n                      <EmojiPicker\r\n                        value={editForm.icon || \"\"}\r\n                        onChange={(emoji) =>\r\n                          setEditForm({ ...editForm, icon: emoji })\r\n                        }\r\n                        placeholder=\"Pick an icon\"\r\n                      />\r\n                      <Textarea\r\n                        value={editForm.description || \"\"}\r\n                        onChange={(e) =>\r\n                          setEditForm({\r\n                            ...editForm,\r\n                            description: e.target.value,\r\n                          })\r\n                        }\r\n                        placeholder=\"Description\"\r\n                        rows={2}\r\n                      />\r\n\r\n                      {/* Color and Status Controls */}\r\n                      <div className=\"flex items-end gap-4\">\r\n                        <div className=\"flex-1\">\r\n                          <Label className=\"text-sm text-gray-600\">Color</Label>\r\n                          <Input\r\n                            type=\"color\"\r\n                            value={editForm.color || \"#3B82F6\"}\r\n                            onChange={(e) =>\r\n                              setEditForm({\r\n                                ...editForm,\r\n                                color: e.target.value,\r\n                              })\r\n                            }\r\n                            className=\"mt-1 h-10\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <Switch\r\n                            className=\"mb-2.5\"\r\n                            checked={editForm.isActive ?? true}\r\n                            onCheckedChange={(checked) =>\r\n                              setEditForm({ ...editForm, isActive: checked })\r\n                            }\r\n                          />\r\n                          <Label className=\"text-sm text-gray-600\">\r\n                            {editForm.isActive ? \"Active\" : \"Inactive\"}\r\n                          </Label>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"flex gap-2\">\r\n                        <Button\r\n                          size=\"sm\"\r\n                          onClick={handleEditSave}\r\n                          disabled={mutationLoading}\r\n                        >\r\n                          <Save className=\"mr-1 h-3 w-3\" />\r\n                          {mutationLoading ? \"Saving...\" : \"Save\"}\r\n                        </Button>\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"outline\"\r\n                          onClick={handleEditCancel}\r\n                          disabled={mutationLoading}\r\n                        >\r\n                          <X className=\"h-3 w-3\" />\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  ) : (\r\n                    // Display mode\r\n                    <div className=\"space-y-3\">\r\n                      <div className=\"flex items-start justify-between\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <span className=\"text-2xl\">{category.icon}</span>\r\n                          <div>\r\n                            <h3 className=\"font-semibold\">{category.name}</h3>\r\n                            <Badge\r\n                              variant={\r\n                                category.isActive ? \"default\" : \"secondary\"\r\n                              }\r\n                              className=\"text-xs\"\r\n                            >\r\n                              {category.isActive ? \"Active\" : \"Inactive\"}\r\n                            </Badge>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <DropdownMenu>\r\n                          <DropdownMenuTrigger asChild>\r\n                            <Button variant=\"ghost\" size=\"sm\">\r\n                              <MoreHorizontal className=\"h-4 w-4\" />\r\n                            </Button>\r\n                          </DropdownMenuTrigger>\r\n                          <DropdownMenuContent align=\"end\">\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleEditStart(category)}\r\n                            >\r\n                              <Edit className=\"mr-2 h-4 w-4\" />\r\n                              Edit\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleViewProducts(category)}\r\n                            >\r\n                              <Eye className=\"mr-2 h-4 w-4\" />\r\n                              View Products\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleDuplicateCategory(category)}\r\n                            >\r\n                              <Copy className=\"mr-2 h-4 w-4\" />\r\n                              Duplicate\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuSeparator />\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleArchiveCategory(category)}\r\n                              className={\r\n                                category.isActive\r\n                                  ? \"text-orange-600\"\r\n                                  : \"text-green-600\"\r\n                              }\r\n                            >\r\n                              <Archive className=\"mr-2 h-4 w-4\" />\r\n                              {category.isActive ? \"Archive\" : \"Unarchive\"}\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem\r\n                              onClick={() => handleDeleteCategory(category.id)}\r\n                              className=\"text-red-600\"\r\n                            >\r\n                              <Trash2 className=\"mr-2 h-4 w-4\" />\r\n                              Delete\r\n                            </DropdownMenuItem>\r\n                          </DropdownMenuContent>\r\n                        </DropdownMenu>\r\n                      </div>\r\n\r\n                      <p className=\"line-clamp-2 text-sm text-gray-600\">\r\n                        {category.description || \"No description provided\"}\r\n                      </p>\r\n\r\n                      <div className=\"flex items-center justify-between text-xs text-gray-500\">\r\n                        <span>{category.productCount} products</span>\r\n                        <span\r\n                          className=\"h-3 w-3 rounded-full\"\r\n                          style={{ backgroundColor: category.color }}\r\n                        ></span>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </CardContent>\r\n              </Card>\r\n            );\r\n          })}\r\n        </div>\r\n      ) : (\r\n        // List view\r\n        <Card>\r\n          <CardContent className=\"p-0\">\r\n            <div className=\"divide-y\">\r\n              {filteredAndSortedCategories.map((category) => {\r\n                console.log(\"Rendering category in list view:\", category); // Debug log\r\n                return (\r\n                  <div key={category.id} className=\"p-4 hover:bg-gray-50\">\r\n                    {editingCategoryId === category.id ? (\r\n                      // Edit form\r\n                      <div className=\"space-y-3\">\r\n                        <div className=\"flex items-center gap-4\">\r\n                          <Input\r\n                            value={editForm.name || \"\"}\r\n                            onChange={(e) =>\r\n                              setEditForm({ ...editForm, name: e.target.value })\r\n                            }\r\n                            placeholder=\"Category name\"\r\n                            className=\"flex-1\"\r\n                          />\r\n                          <Input\r\n                            value={editForm.description || \"\"}\r\n                            onChange={(e) =>\r\n                              setEditForm({\r\n                                ...editForm,\r\n                                description: e.target.value,\r\n                              })\r\n                            }\r\n                            placeholder=\"Description\"\r\n                            className=\"flex-1\"\r\n                          />\r\n                        </div>\r\n                        <div className=\"max-w-xs\">\r\n                          <EmojiPicker\r\n                            value={editForm.icon || \"\"}\r\n                            onChange={(emoji) =>\r\n                              setEditForm({ ...editForm, icon: emoji })\r\n                            }\r\n                            placeholder=\"Pick an icon\"\r\n                          />\r\n                        </div>\r\n\r\n                        {/* Color and Status Controls for List View */}\r\n                        <div className=\"flex items-center gap-4\">\r\n                          <div className=\"max-w-xs\">\r\n                            <Label className=\"text-sm text-gray-600\">\r\n                              Color\r\n                            </Label>\r\n                            <Input\r\n                              type=\"color\"\r\n                              value={editForm.color || \"#3B82F6\"}\r\n                              onChange={(e) =>\r\n                                setEditForm({\r\n                                  ...editForm,\r\n                                  color: e.target.value,\r\n                                })\r\n                              }\r\n                              className=\"mt-1 h-10\"\r\n                            />\r\n                          </div>\r\n                          <div className=\"flex items-center gap-2\">\r\n                            <Switch\r\n                              checked={editForm.isActive ?? true}\r\n                              onCheckedChange={(checked) =>\r\n                                setEditForm({ ...editForm, isActive: checked })\r\n                              }\r\n                            />\r\n                            <Label className=\"text-sm text-gray-600\">\r\n                              {editForm.isActive ? \"Active\" : \"Inactive\"}\r\n                            </Label>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"flex gap-2\">\r\n                          <Button size=\"sm\" onClick={handleEditSave}>\r\n                            <Save className=\"h-4 w-4\" />\r\n                          </Button>\r\n                          <Button\r\n                            size=\"sm\"\r\n                            variant=\"outline\"\r\n                            onClick={handleEditCancel}\r\n                          >\r\n                            <X className=\"h-4 w-4\" />\r\n                          </Button>\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      // Display mode\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <div className=\"flex items-center gap-4\">\r\n                          <span className=\"text-2xl\">{category.icon}</span>\r\n                          <div>\r\n                            <div className=\"flex items-center gap-2\">\r\n                              <h3 className=\"font-semibold\">{category.name}</h3>\r\n                              <Badge\r\n                                variant={\r\n                                  category.isActive ? \"default\" : \"secondary\"\r\n                                }\r\n                                className=\"text-xs\"\r\n                              >\r\n                                {category.isActive ? \"Active\" : \"Inactive\"}\r\n                              </Badge>\r\n                            </div>\r\n                            <p className=\"text-sm text-gray-600\">\r\n                              {category.description || \"No description\"}\r\n                            </p>\r\n                            <div className=\"mt-1 flex items-center gap-4 text-xs text-gray-500\">\r\n                              <span>{category.productCount} products</span>\r\n                              <span>Slug: {category.slug}</span>\r\n                              <div className=\"flex items-center gap-1\">\r\n                                <span>Color:</span>\r\n                                <span\r\n                                  className=\"h-3 w-3 rounded-full\"\r\n                                  style={{ backgroundColor: category.color }}\r\n                                ></span>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n\r\n                        <div className=\"flex gap-2\">\r\n                          <Button\r\n                            size=\"sm\"\r\n                            variant=\"outline\"\r\n                            onClick={() => handleEditStart(category)}\r\n                          >\r\n                            <Pencil className=\"h-4 w-4\" />\r\n                          </Button>\r\n                          <Button\r\n                            size=\"sm\"\r\n                            variant=\"outline\"\r\n                            onClick={() => handleDeleteCategory(category.id)}\r\n                          >\r\n                            <Trash2 className=\"h-4 w-4\" />\r\n                          </Button>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n\r\n      {/* Empty State */}\r\n      {filteredAndSortedCategories.length === 0 && (\r\n        <Card>\r\n          <CardContent className=\"p-12 text-center\">\r\n            <FolderOpen className=\"mx-auto h-12 w-12 text-gray-400\" />\r\n            <h3 className=\"mt-2 text-lg font-medium text-gray-900\">\r\n              No categories found\r\n            </h3>\r\n            <p className=\"mt-1 text-sm text-gray-500\">\r\n              {searchQuery || filterStatus !== \"all\"\r\n                ? \"Try adjusting your search or filter criteria.\"\r\n                : \"Get started by creating your first category.\"}\r\n            </p>\r\n            {!searchQuery && filterStatus === \"all\" && (\r\n              <Button className=\"mt-4\" onClick={() => setShowAddForm(true)}>\r\n                <Plus className=\"mr-2 h-4 w-4\" />\r\n                Add Your First Category\r\n              </Button>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA;AACA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAvDA;;;;;;;;;;;;;;;;;;AAgFO,MAAM,0BAA0B,CAAC,EACtC,oBAAoB,EAAE,EACtB,kBAAkB,EACG;IACrB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,YAAY;IACZ,MAAM,EACJ,YAAY,aAAa,EACzB,OAAO,EACP,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD;IAChB,MAAM,EACJ,cAAc,EACd,cAAc,EACd,cAAc,EACd,SAAS,eAAe,EACzB,GAAG,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IAEvB,cAAc;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAqB;QAChE,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EACvD;IAEF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,gDAAgD;IAChD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,cAAc;YACd,IAAI,oBAAoB;gBACtB,mBAAmB;YACrB;QACF,OAAO,IAAI,kBAAkB,MAAM,GAAG,GAAG;YACvC,cAAc;QAChB;IACF,GAAG;QAAC;QAAe;QAAmB;KAAmB;IAEzD,yCAAyC;IACzC,MAAM,eAAe,CAAC;QACpB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,UAAU;IACvB;IAEA,6BAA6B;IAC7B,MAAM,8BAA8B,WACjC,MAAM,CAAC,CAAC;QACP,MAAM,gBACJ,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QACrE,MAAM,gBACJ,iBAAiB,SAChB,iBAAiB,YAAY,SAAS,QAAQ,IAC9C,iBAAiB,cAAc,CAAC,SAAS,QAAQ;QACpD,OAAO,iBAAiB;IAC1B,GACC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;gBACxC;YACF,KAAK;gBACH,aAAa,EAAE,YAAY,GAAG,EAAE,YAAY;gBAC5C;YACF,KAAK;gBACH,aACE,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACjE;YACF;gBACE,aAAa,EAAE,SAAS,GAAG,EAAE,SAAS;QAC1C;QACA,OAAO,cAAc,QAAQ,aAAa,CAAC;IAC7C;IAEF,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,IAAI,EAAE;YACrB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,eAAe;gBACnB,MAAM,YAAY,IAAI;gBACtB,aAAa,YAAY,WAAW,IAAI;gBACxC,MAAM,YAAY,IAAI,IAAI;gBAC1B,OAAO,YAAY,KAAK,IAAI;gBAC5B,UAAU,YAAY,QAAQ,IAAI;YACpC;YAEA,aAAa;YACb,eAAe;gBACb,MAAM;gBACN,aAAa;gBACb,MAAM;gBACN,OAAO;gBACP,UAAU;YACZ;YACA,eAAe;YAEf,0BAA0B;YAC1B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,uCAAuC;YACvC,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,2BAA2B;IAC3B,MAAM,kBAAkB,CAAC;QACvB,qBAAqB,SAAS,EAAE;QAChC,YAAY;YAAE,GAAG,QAAQ;QAAC;IAC5B;IAEA,iBAAiB;IACjB,MAAM,mBAAmB;QACvB,qBAAqB;QACrB,YAAY,CAAC;IACf;IAEA,uBAAuB;IACvB,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,mBAAmB;YACxC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,eAAe,mBAAmB;gBACtC,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW;gBACjC,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;YAC7B;YAEA,sBAAsB;YACtB,qBAAqB;YACrB,YAAY,CAAC;YAEb,0BAA0B;YAC1B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,uCAAuC;YACvC,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,oBAAoB;IACpB,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,QAAQ,GAAG,CAAC,0CAA0C,aAAa,YAAY;YAC/E,QAAQ,GAAG,CAAC,uBAAuB,OAAO,aAAa,YAAY;YAEnE,IAAI,CAAC,YAAY;gBACf,QAAQ,KAAK,CAAC;gBACd;YACF;YAEA,MAAM,eAAe;YAErB,0BAA0B;YAC1B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,uCAAuC;YACvC,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,gDAAgD;IAChD,MAAM,iCAAiC;QACrC,IAAI;YACF,MAAM,yHAAA,CAAA,qBAAkB,CAAC,wBAAwB;YACjD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,iDAAiD;YACjD,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB,CAAC;QAC1B,iDAAiD;QACjD,OAAO,IAAI,CACT,CAAC,yBAAyB,EAAE,mBAAmB,SAAS,IAAI,GAAG;QAEjE,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,qBAAqB,EAAE,SAAS,IAAI,CAAC,UAAU,CAAC;IACjE;IAEA,uBAAuB;IACvB,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,MAAM,iBAAiB,GAAG,SAAS,IAAI,CAAC,OAAO,CAAC;YAEhD,MAAM,eAAe;gBACnB,MAAM;gBACN,aAAa,SAAS,WAAW,IAAI;gBACrC,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,SAAS,KAAK,IAAI;gBACzB,UAAU,SAAS,QAAQ;YAC7B;YAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,eAAe,sBAAsB,CAAC;YAEjE,0BAA0B;YAC1B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,wDAAwD;IACxD,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,MAAM,YAAY,CAAC,SAAS,QAAQ;YACpC,MAAM,SAAS,YAAY,eAAe;YAE1C,MAAM,eAAe,SAAS,EAAE,EAAE;gBAChC,UAAU;YACZ;YAEA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,OAAO,aAAa,CAAC;YAElE,0BAA0B;YAC1B,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBAAqB;IACrB,IAAI,WAAW,WAAW,MAAM,KAAK,GAAG;QACtC,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC;4BAAI,WAAU;;;;;;sCACf,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6WAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;IAOpD;IAEA,mBAAmB;IACnB,IAAI,SAAS,WAAW,MAAM,KAAK,GAAG;QACpC,qBACE,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAEf,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6WAAC;4BAAE,WAAU;sCAA8B;;;;;;sCAC3C,6WAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;4BAAO,SAAS,IAAM;sCAAW;;;;;;;;;;;;;;;;;;;;;;IAO7D;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM5D,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,WAAW,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/D,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DACV,WAAW,MAAM,GAAG,IACjB,KAAK,KAAK,CACR,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE,KAClD,WAAW,MAAM,IAErB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;;;;;;;kDAId,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe;;0DAC1C,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAQ,eAAe;;0DACpC,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS,cAAc,QAAQ,YAAY;wCAC3C,MAAK;wCACL,SAAS,IACP,aAAa,cAAc,QAAQ,SAAS;kDAG7C,cAAc,sBACb,6WAAC,kTAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAEnB,6WAAC,qTAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAIxB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,gSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIpB,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU,mBAAmB;;0DAE7B,6WAAC,oSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIxC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,eAAe;;0DACpC,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,6BACC,6WAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6WAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAA0B;;;;;;;;;;;;kCAI9C,6WAAC,yHAAA,CAAA,cAAW;;0CACV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IACT,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAExD,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,6WAAC;kDACC,cAAA,6WAAC,sIAAA,CAAA,cAAW;4CACV,OAAO,YAAY,IAAI;4CACvB,UAAU,CAAC,QACT,eAAe;oDAAE,GAAG,WAAW;oDAAE,MAAM;gDAAM;4CAE/C,aAAY;;;;;;;;;;;kDAIhB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAA2B;;;;;;0DAC1C,6WAAC,6HAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO,YAAY,WAAW;gDAC9B,UAAU,CAAC,IACT,eAAe;wDACb,GAAG,WAAW;wDACd,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC7B;gDAEF,aAAY;gDACZ,WAAU;gDACV,MAAM;;;;;;;;;;;;kDAIV,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAqB;;;;;;0DACpC,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,YAAY,KAAK;gDACxB,UAAU,CAAC,IACT,eAAe;wDAAE,GAAG,WAAW;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAEzD,WAAU;;;;;;;;;;;;kDAId,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,IAAG;gDACH,SAAS,YAAY,QAAQ;gDAC7B,iBAAiB,CAAC,UAChB,eAAe;wDAAE,GAAG,WAAW;wDAAE,UAAU;oDAAQ;;;;;;0DAGvD,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAsB;;;;;;;;;;;;;;;;;;0CAIzC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,mBAAmB;;0DAE7B,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,kBAAkB,cAAc;;;;;;;kDAEnC,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,eAAe;wCAC9B,UAAU;;0DAEV,6WAAC,gRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YASvC,aAAa,uBACZ,6WAAC;gBAAI,WAAU;0BACZ,4BAA4B,GAAG,CAAC,CAAC;oBAChC,QAAQ,GAAG,CAAC,uBAAuB,WAAW,YAAY;oBAC1D,qBACE,6WAAC,yHAAA,CAAA,OAAI;wBAEH,WAAU;kCAEV,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,sBAAsB,SAAS,EAAE,GAChC,YAAY;0CACZ,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0HAAA,CAAA,QAAK;wCACJ,OAAO,SAAS,IAAI,IAAI;wCACxB,UAAU,CAAC,IACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAElD,aAAY;;;;;;kDAEd,6WAAC,sIAAA,CAAA,cAAW;wCACV,OAAO,SAAS,IAAI,IAAI;wCACxB,UAAU,CAAC,QACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,MAAM;4CAAM;wCAEzC,aAAY;;;;;;kDAEd,6WAAC,6HAAA,CAAA,WAAQ;wCACP,OAAO,SAAS,WAAW,IAAI;wCAC/B,UAAU,CAAC,IACT,YAAY;gDACV,GAAG,QAAQ;gDACX,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC7B;wCAEF,aAAY;wCACZ,MAAM;;;;;;kDAIR,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,0HAAA,CAAA,QAAK;wDAAC,WAAU;kEAAwB;;;;;;kEACzC,6WAAC,0HAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,SAAS,KAAK,IAAI;wDACzB,UAAU,CAAC,IACT,YAAY;gEACV,GAAG,QAAQ;gEACX,OAAO,EAAE,MAAM,CAAC,KAAK;4DACvB;wDAEF,WAAU;;;;;;;;;;;;0DAGd,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,2HAAA,CAAA,SAAM;wDACL,WAAU;wDACV,SAAS,SAAS,QAAQ,IAAI;wDAC9B,iBAAiB,CAAC,UAChB,YAAY;gEAAE,GAAG,QAAQ;gEAAE,UAAU;4DAAQ;;;;;;kEAGjD,6WAAC,0HAAA,CAAA,QAAK;wDAAC,WAAU;kEACd,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;kDAKtC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,UAAU;;kEAEV,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,kBAAkB,cAAc;;;;;;;0DAEnC,6WAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;gDACT,UAAU;0DAEV,cAAA,6WAAC,gRAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;uCAKnB,eAAe;0CACf,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAK,WAAU;kEAAY,SAAS,IAAI;;;;;;kEACzC,6WAAC;;0EACC,6WAAC;gEAAG,WAAU;0EAAiB,SAAS,IAAI;;;;;;0EAC5C,6WAAC,0HAAA,CAAA,QAAK;gEACJ,SACE,SAAS,QAAQ,GAAG,YAAY;gEAElC,WAAU;0EAET,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;0DAKtC,6WAAC,qIAAA,CAAA,eAAY;;kEACX,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,6WAAC,qIAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,gBAAgB;;kFAE/B,6WAAC,+RAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,mBAAmB;;kFAElC,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,wBAAwB;;kFAEvC,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;0EACtB,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,sBAAsB;gEACrC,WACE,SAAS,QAAQ,GACb,oBACA;;kFAGN,6WAAC,4RAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAClB,SAAS,QAAQ,GAAG,YAAY;;;;;;;0EAEnC,6WAAC,qIAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,qBAAqB,SAAS,EAAE;gEAC/C,WAAU;;kFAEV,6WAAC,8RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kDAO3C,6WAAC;wCAAE,WAAU;kDACV,SAAS,WAAW,IAAI;;;;;;kDAG3B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;;oDAAM,SAAS,YAAY;oDAAC;;;;;;;0DAC7B,6WAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,SAAS,KAAK;gDAAC;;;;;;;;;;;;;;;;;;;;;;;uBA7J9C,SAAS,EAAE;;;;;gBAqKtB;;;;;uBAGF,YAAY;0BACZ,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;kCACZ,4BAA4B,GAAG,CAAC,CAAC;4BAChC,QAAQ,GAAG,CAAC,oCAAoC,WAAW,YAAY;4BACvE,qBACE,6WAAC;gCAAsB,WAAU;0CAC9B,sBAAsB,SAAS,EAAE,GAChC,YAAY;8CACZ,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,0HAAA,CAAA,QAAK;oDACJ,OAAO,SAAS,IAAI,IAAI;oDACxB,UAAU,CAAC,IACT,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAElD,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6WAAC,0HAAA,CAAA,QAAK;oDACJ,OAAO,SAAS,WAAW,IAAI;oDAC/B,UAAU,CAAC,IACT,YAAY;4DACV,GAAG,QAAQ;4DACX,aAAa,EAAE,MAAM,CAAC,KAAK;wDAC7B;oDAEF,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAGd,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC,sIAAA,CAAA,cAAW;gDACV,OAAO,SAAS,IAAI,IAAI;gDACxB,UAAU,CAAC,QACT,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM;oDAAM;gDAEzC,aAAY;;;;;;;;;;;sDAKhB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,0HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAwB;;;;;;sEAGzC,6WAAC,0HAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,OAAO,SAAS,KAAK,IAAI;4DACzB,UAAU,CAAC,IACT,YAAY;oEACV,GAAG,QAAQ;oEACX,OAAO,EAAE,MAAM,CAAC,KAAK;gEACvB;4DAEF,WAAU;;;;;;;;;;;;8DAGd,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,2HAAA,CAAA,SAAM;4DACL,SAAS,SAAS,QAAQ,IAAI;4DAC9B,iBAAiB,CAAC,UAChB,YAAY;oEAAE,GAAG,QAAQ;oEAAE,UAAU;gEAAQ;;;;;;sEAGjD,6WAAC,0HAAA,CAAA,QAAK;4DAAC,WAAU;sEACd,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;sDAKtC,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,2HAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAS;8DACzB,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS;8DAET,cAAA,6WAAC,gRAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;2CAKnB,eAAe;8CACf,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAK,WAAU;8DAAY,SAAS,IAAI;;;;;;8DACzC,6WAAC;;sEACC,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAG,WAAU;8EAAiB,SAAS,IAAI;;;;;;8EAC5C,6WAAC,0HAAA,CAAA,QAAK;oEACJ,SACE,SAAS,QAAQ,GAAG,YAAY;oEAElC,WAAU;8EAET,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;sEAGpC,6WAAC;4DAAE,WAAU;sEACV,SAAS,WAAW,IAAI;;;;;;sEAE3B,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;;wEAAM,SAAS,YAAY;wEAAC;;;;;;;8EAC7B,6WAAC;;wEAAK;wEAAO,SAAS,IAAI;;;;;;;8EAC1B,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;sFAAK;;;;;;sFACN,6WAAC;4EACC,WAAU;4EACV,OAAO;gFAAE,iBAAiB,SAAS,KAAK;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOnD,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,gBAAgB;8DAE/B,cAAA,6WAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6WAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,qBAAqB,SAAS,EAAE;8DAE/C,cAAA,6WAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BA9HlB,SAAS,EAAE;;;;;wBAqIzB;;;;;;;;;;;;;;;;YAOP,4BAA4B,MAAM,KAAK,mBACtC,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC,sSAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,6WAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6WAAC;4BAAE,WAAU;sCACV,eAAe,iBAAiB,QAC7B,kDACA;;;;;;wBAEL,CAAC,eAAe,iBAAiB,uBAChC,6WAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;4BAAO,SAAS,IAAM,eAAe;;8CACrD,6WAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}, {"offset": {"line": 6991, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/materials/MaterialManagerEnhanced.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport {\n  Archive,\n  Check,\n  Copy,\n  Edit,\n  Eye,\n  Filter,\n  Grid3X3,\n  Hammer,\n  List,\n  MoreHorizontal,\n  Package,\n  Pencil,\n  Plus,\n  Save,\n  Search,\n  SortAsc,\n  SortDesc,\n  Star,\n  Trash2,\n  TrendingUp,\n  X,\n} from \"lucide-react\";\nimport { toast } from \"sonner\";\n\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { useMaterials } from \"@/hooks/useMaterials\";\nimport { Material, CreateMaterialDto } from \"@/lib/api/materials\";\n\ninterface MaterialManagerEnhancedProps {\n  initialMaterials?: Material[];\n  onMaterialsChange?: (materials: Material[]) => void;\n}\n\n/**\n * Enhanced component for managing product materials with real API integration\n */\nexport const MaterialManagerEnhanced = ({\n  initialMaterials = [],\n  onMaterialsChange,\n}: MaterialManagerEnhancedProps) => {\n  const router = useRouter();\n\n  // API hooks\n  const {\n    materials: apiMaterials,\n    loading,\n    error,\n    createMaterial,\n    updateMaterial,\n    deleteMaterial,\n    refreshMaterials,\n  } = useMaterials();\n\n  // Local state\n  const [materials, setMaterials] = useState<Material[]>(initialMaterials);\n  const [newMaterial, setNewMaterial] = useState<Partial<CreateMaterialDto>>({\n    name: \"\",\n    description: \"\",\n    type: \"natural\",\n    properties: [],\n    isActive: true,\n  });\n  const [editingMaterialId, setEditingMaterialId] = useState<string | null>(null);\n  const [editForm, setEditForm] = useState<Partial<Material>>({});\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [showInactive, setShowInactive] = useState(false);\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\n  const [sortBy, setSortBy] = useState<\"name\" | \"created\" | \"products\">(\"name\");\n\n  // Update local materials when API data changes\n  useEffect(() => {\n    setMaterials(apiMaterials);\n    if (onMaterialsChange) {\n      onMaterialsChange(apiMaterials);\n    }\n  }, [apiMaterials, onMaterialsChange]);\n\n  // Filter and sort materials\n  const filteredMaterials = materials\n    .filter((material) => {\n      const matchesSearch = material.name\n        .toLowerCase()\n        .includes(searchTerm.toLowerCase());\n      const matchesStatus = showInactive || material.isActive;\n      return matchesSearch && matchesStatus;\n    })\n    .sort((a, b) => {\n      switch (sortBy) {\n        case \"name\":\n          return a.name.localeCompare(b.name);\n        case \"created\":\n          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n        case \"products\":\n          return (b.productCount || 0) - (a.productCount || 0);\n        default:\n          return 0;\n      }\n    });\n\n  // Handle create material\n  const handleCreateMaterial = async () => {\n    if (!newMaterial.name?.trim()) {\n      toast.error(\"Material name is required\");\n      return;\n    }\n\n    try {\n      await createMaterial(newMaterial as CreateMaterialDto);\n      setNewMaterial({\n        name: \"\",\n        description: \"\",\n        type: \"natural\",\n        properties: [],\n        isActive: true,\n      });\n      setShowAddForm(false);\n      refreshMaterials();\n    } catch (error) {\n      console.error(\"Error creating material:\", error);\n    }\n  };\n\n  // Handle update material\n  const handleUpdateMaterial = async () => {\n    if (!editingMaterialId || !editForm.name?.trim()) {\n      toast.error(\"Material name is required\");\n      return;\n    }\n\n    try {\n      await updateMaterial(editingMaterialId, editForm);\n      setEditingMaterialId(null);\n      setEditForm({});\n      refreshMaterials();\n    } catch (error) {\n      console.error(\"Error updating material:\", error);\n    }\n  };\n\n  // Handle delete material\n  const handleDeleteMaterial = async (materialId: string) => {\n    if (!confirm(\"Are you sure you want to delete this material?\")) {\n      return;\n    }\n\n    try {\n      await deleteMaterial(materialId);\n      refreshMaterials();\n    } catch (error) {\n      console.error(\"Error deleting material:\", error);\n    }\n  };\n\n  // Statistics\n  const stats = {\n    total: materials.length,\n    active: materials.filter((m) => m.isActive).length,\n    natural: materials.filter((m) => m.type === \"natural\").length,\n    synthetic: materials.filter((m) => m.type === \"synthetic\").length,\n  };\n\n  if (error) {\n    return (\n      <div className=\"rounded-lg border border-red-200 bg-red-50 p-4\">\n        <p className=\"text-red-600\">Error loading materials: {error}</p>\n        <Button onClick={refreshMaterials} className=\"mt-2\">\n          Retry\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Statistics */}\n      <div className=\"grid gap-4 md:grid-cols-4\">\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-purple-100 p-2\">\n                <Hammer className=\"h-5 w-5 text-purple-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Total Materials</p>\n                <p className=\"text-2xl font-bold\">{stats.total}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-green-100 p-2\">\n                <TrendingUp className=\"h-5 w-5 text-green-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Active Materials</p>\n                <p className=\"text-2xl font-bold\">{stats.active}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-blue-100 p-2\">\n                <Star className=\"h-5 w-5 text-blue-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Natural</p>\n                <p className=\"text-2xl font-bold\">{stats.natural}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"rounded-full bg-orange-100 p-2\">\n                <Package className=\"h-5 w-5 text-orange-600\" />\n              </div>\n              <div>\n                <p className=\"text-sm text-gray-600\">Synthetic</p>\n                <p className=\"text-2xl font-bold\">{stats.synthetic}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Controls */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between\">\n            {/* Search and Filters */}\n            <div className=\"flex flex-1 gap-4\">\n              <div className=\"relative max-w-md flex-1\">\n                <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400\" />\n                <Input\n                  placeholder=\"Search materials...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n\n              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>\n                <SelectTrigger className=\"w-40\">\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"name\">Name</SelectItem>\n                  <SelectItem value=\"created\">Created</SelectItem>\n                  <SelectItem value=\"products\">Products</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* Actions */}\n            <div className=\"flex gap-2\">\n              <div className=\"flex items-center gap-2\">\n                <Switch\n                  checked={showInactive}\n                  onCheckedChange={setShowInactive}\n                />\n                <Label className=\"text-sm\">Show inactive</Label>\n              </div>\n\n              <div className=\"flex gap-1\">\n                <Button\n                  variant={viewMode === \"grid\" ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"grid\")}\n                >\n                  <Grid3X3 className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  variant={viewMode === \"list\" ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  onClick={() => setViewMode(\"list\")}\n                >\n                  <List className=\"h-4 w-4\" />\n                </Button>\n              </div>\n\n              <Button onClick={() => setShowAddForm(true)}>\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Add Material\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Add Material Form */}\n      {showAddForm && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Add New Material</CardTitle>\n            <CardDescription>\n              Create a new material for your product catalog\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid gap-4 md:grid-cols-2\">\n              <div>\n                <Label htmlFor=\"material-name\">Name *</Label>\n                <Input\n                  id=\"material-name\"\n                  value={newMaterial.name || \"\"}\n                  onChange={(e) =>\n                    setNewMaterial({ ...newMaterial, name: e.target.value })\n                  }\n                  placeholder=\"e.g., Organic Cotton\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"material-type\">Type</Label>\n                <Select\n                  value={newMaterial.type}\n                  onValueChange={(value: any) =>\n                    setNewMaterial({ ...newMaterial, type: value })\n                  }\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"natural\">Natural</SelectItem>\n                    <SelectItem value=\"synthetic\">Synthetic</SelectItem>\n                    <SelectItem value=\"blend\">Blend</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n\n            <div>\n              <Label htmlFor=\"material-description\">Description</Label>\n              <Textarea\n                id=\"material-description\"\n                value={newMaterial.description || \"\"}\n                onChange={(e) =>\n                  setNewMaterial({ ...newMaterial, description: e.target.value })\n                }\n                placeholder=\"Describe the material properties and uses...\"\n                rows={3}\n              />\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <Switch\n                checked={newMaterial.isActive}\n                onCheckedChange={(checked) =>\n                  setNewMaterial({ ...newMaterial, isActive: checked })\n                }\n              />\n              <Label>Active</Label>\n            </div>\n\n            <div className=\"flex gap-2 pt-2\">\n              <Button onClick={handleCreateMaterial}>\n                <Check className=\"mr-2 h-4 w-4\" />\n                Add Material\n              </Button>\n              <Button variant=\"outline\" onClick={() => setShowAddForm(false)}>\n                <X className=\"mr-2 h-4 w-4\" />\n                Cancel\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Materials Display */}\n      {loading ? (\n        <div className=\"space-y-2\">\n          {[...Array(5)].map((_, i) => (\n            <Skeleton key={i} className=\"h-20 w-full\" />\n          ))}\n        </div>\n      ) : filteredMaterials.length === 0 ? (\n        <Card>\n          <CardContent className=\"py-8 text-center\">\n            <p className=\"text-muted-foreground\">No materials found</p>\n            {searchTerm && (\n              <Button\n                variant=\"link\"\n                onClick={() => setSearchTerm(\"\")}\n                className=\"mt-2\"\n              >\n                Clear search\n              </Button>\n            )}\n          </CardContent>\n        </Card>\n      ) : (\n        <div\n          className={\n            viewMode === \"grid\"\n              ? \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\"\n              : \"space-y-3\"\n          }\n        >\n          {filteredMaterials.map((material) => (\n            <Card\n              key={material._id}\n              className={`transition-shadow hover:shadow-md ${\n                !material.isActive ? \"opacity-60\" : \"\"\n              }`}\n            >\n              <CardContent className=\"p-4\">\n                {editingMaterialId === material._id ? (\n                  // Edit form\n                  <div className=\"space-y-3\">\n                    <Input\n                      value={editForm.name || \"\"}\n                      onChange={(e) =>\n                        setEditForm({ ...editForm, name: e.target.value })\n                      }\n                      placeholder=\"Material name\"\n                    />\n                    <Textarea\n                      value={editForm.description || \"\"}\n                      onChange={(e) =>\n                        setEditForm({ ...editForm, description: e.target.value })\n                      }\n                      placeholder=\"Description\"\n                      rows={2}\n                    />\n                    <div className=\"flex gap-2\">\n                      <Button size=\"sm\" onClick={handleUpdateMaterial}>\n                        <Save className=\"mr-2 h-4 w-4\" />\n                        Save\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => {\n                          setEditingMaterialId(null);\n                          setEditForm({});\n                        }}\n                      >\n                        <X className=\"mr-2 h-4 w-4\" />\n                        Cancel\n                      </Button>\n                    </div>\n                  </div>\n                ) : (\n                  // Display mode\n                  <div>\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <h3 className=\"font-semibold text-gray-900\">\n                          {material.name}\n                        </h3>\n                        {material.description && (\n                          <p className=\"mt-1 text-sm text-gray-600\">\n                            {material.description}\n                          </p>\n                        )}\n                        <div className=\"mt-2 flex items-center gap-2\">\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            {material.type}\n                          </Badge>\n                          {!material.isActive && (\n                            <Badge variant=\"secondary\" className=\"text-xs\">\n                              Inactive\n                            </Badge>\n                          )}\n                          <span className=\"text-xs text-gray-500\">\n                            {material.productCount || 0} products\n                          </span>\n                        </div>\n                      </div>\n\n                      <DropdownMenu>\n                        <DropdownMenuTrigger asChild>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <MoreHorizontal className=\"h-4 w-4\" />\n                          </Button>\n                        </DropdownMenuTrigger>\n                        <DropdownMenuContent align=\"end\">\n                          <DropdownMenuItem\n                            onClick={() => {\n                              setEditingMaterialId(material._id);\n                              setEditForm(material);\n                            }}\n                          >\n                            <Edit className=\"mr-2 h-4 w-4\" />\n                            Edit\n                          </DropdownMenuItem>\n                          <DropdownMenuItem>\n                            <Eye className=\"mr-2 h-4 w-4\" />\n                            View Products\n                          </DropdownMenuItem>\n                          <DropdownMenuItem>\n                            <Copy className=\"mr-2 h-4 w-4\" />\n                            Duplicate\n                          </DropdownMenuItem>\n                          <DropdownMenuSeparator />\n                          <DropdownMenuItem\n                            onClick={() => handleDeleteMaterial(material._id)}\n                            className=\"text-red-600\"\n                          >\n                            <Trash2 className=\"mr-2 h-4 w-4\" />\n                            Delete\n                          </DropdownMenuItem>\n                        </DropdownMenuContent>\n                      </DropdownMenu>\n                    </div>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AAEA;AACA;AACA;AAOA;AAOA;AACA;AACA;AAOA;AACA;AACA;AACA;AAzDA;;;;;;;;;;;;;;;;;AAoEO,MAAM,0BAA0B,CAAC,EACtC,mBAAmB,EAAE,EACrB,iBAAiB,EACY;IAC7B,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IAEvB,YAAY;IACZ,MAAM,EACJ,WAAW,YAAY,EACvB,OAAO,EACP,KAAK,EACL,cAAc,EACd,cAAc,EACd,cAAc,EACd,gBAAgB,EACjB,GAAG,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;IAEf,cAAc;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAc;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA8B;QACzE,MAAM;QACN,aAAa;QACb,MAAM;QACN,YAAY,EAAE;QACd,UAAU;IACZ;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmC;IAEtE,+CAA+C;IAC/C,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QACb,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAc;KAAkB;IAEpC,4BAA4B;IAC5B,MAAM,oBAAoB,UACvB,MAAM,CAAC,CAAC;QACP,MAAM,gBAAgB,SAAS,IAAI,CAChC,WAAW,GACX,QAAQ,CAAC,WAAW,WAAW;QAClC,MAAM,gBAAgB,gBAAgB,SAAS,QAAQ;QACvD,OAAO,iBAAiB;IAC1B,GACC,IAAI,CAAC,CAAC,GAAG;QACR,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;YACpC,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YACxE,KAAK;gBACH,OAAO,CAAC,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC;YACrD;gBACE,OAAO;QACX;IACF;IAEF,yBAAyB;IACzB,MAAM,uBAAuB;QAC3B,IAAI,CAAC,YAAY,IAAI,EAAE,QAAQ;YAC7B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,eAAe;YACrB,eAAe;gBACb,MAAM;gBACN,aAAa;gBACb,MAAM;gBACN,YAAY,EAAE;gBACd,UAAU;YACZ;YACA,eAAe;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,yBAAyB;IACzB,MAAM,uBAAuB;QAC3B,IAAI,CAAC,qBAAqB,CAAC,SAAS,IAAI,EAAE,QAAQ;YAChD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,eAAe,mBAAmB;YACxC,qBAAqB;YACrB,YAAY,CAAC;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,yBAAyB;IACzB,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,QAAQ,mDAAmD;YAC9D;QACF;QAEA,IAAI;YACF,MAAM,eAAe;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,aAAa;IACb,MAAM,QAAQ;QACZ,OAAO,UAAU,MAAM;QACvB,QAAQ,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;QAClD,SAAS,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,WAAW,MAAM;QAC7D,WAAW,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,aAAa,MAAM;IACnE;IAEA,IAAI,OAAO;QACT,qBACE,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAE,WAAU;;wBAAe;wBAA0B;;;;;;;8BACtD,6WAAC,2HAAA,CAAA,SAAM;oBAAC,SAAS;oBAAkB,WAAU;8BAAO;;;;;;;;;;;;IAK1D;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMtD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMvD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMxD,6WAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6WAAC;;0DACC,6WAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6WAAC;gDAAE,WAAU;0DAAsB,MAAM,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5D,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6WAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAId,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAQ,eAAe,CAAC,QAAe,UAAU;;0DAC9D,6WAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,2HAAA,CAAA,gBAAa;;kEACZ,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6WAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;;;;;;;0CAMnC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS;gDACT,iBAAiB;;;;;;0DAEnB,6WAAC,0HAAA,CAAA,QAAK;gDAAC,WAAU;0DAAU;;;;;;;;;;;;kDAG7B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;0DAE3B,cAAA,6WAAC,gSAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6WAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;0DAE3B,cAAA,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIpB,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,eAAe;;0DACpC,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1C,6BACC,6WAAC,yHAAA,CAAA,OAAI;;kCACH,6WAAC,yHAAA,CAAA,aAAU;;0CACT,6WAAC,yHAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6WAAC,yHAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6WAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,6WAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,YAAY,IAAI,IAAI;gDAC3B,UAAU,CAAC,IACT,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAExD,aAAY;;;;;;;;;;;;kDAGhB,6WAAC;;0DACC,6WAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,6WAAC,2HAAA,CAAA,SAAM;gDACL,OAAO,YAAY,IAAI;gDACvB,eAAe,CAAC,QACd,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM;oDAAM;;kEAG/C,6WAAC,2HAAA,CAAA,gBAAa;kEACZ,cAAA,6WAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6WAAC,2HAAA,CAAA,gBAAa;;0EACZ,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAY;;;;;;0EAC9B,6WAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMlC,6WAAC;;kDACC,6WAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAuB;;;;;;kDACtC,6WAAC,6HAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,YAAY,WAAW,IAAI;wCAClC,UAAU,CAAC,IACT,eAAe;gDAAE,GAAG,WAAW;gDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAE/D,aAAY;wCACZ,MAAM;;;;;;;;;;;;0CAIV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCACL,SAAS,YAAY,QAAQ;wCAC7B,iBAAiB,CAAC,UAChB,eAAe;gDAAE,GAAG,WAAW;gDAAE,UAAU;4CAAQ;;;;;;kDAGvD,6WAAC,0HAAA,CAAA,QAAK;kDAAC;;;;;;;;;;;;0CAGT,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAS;;0DACf,6WAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS,IAAM,eAAe;;0DACtD,6WAAC,gRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YASvC,wBACC,6WAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6WAAC,6HAAA,CAAA,WAAQ;wBAAS,WAAU;uBAAb;;;;;;;;;uBAGjB,kBAAkB,MAAM,KAAK,kBAC/B,6WAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC;4BAAE,WAAU;sCAAwB;;;;;;wBACpC,4BACC,6WAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,cAAc;4BAC7B,WAAU;sCACX;;;;;;;;;;;;;;;;qCAOP,6WAAC;gBACC,WACE,aAAa,SACT,6CACA;0BAGL,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6WAAC,yHAAA,CAAA,OAAI;wBAEH,WAAW,CAAC,kCAAkC,EAC5C,CAAC,SAAS,QAAQ,GAAG,eAAe,IACpC;kCAEF,cAAA,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,sBAAsB,SAAS,GAAG,GACjC,YAAY;0CACZ,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0HAAA,CAAA,QAAK;wCACJ,OAAO,SAAS,IAAI,IAAI;wCACxB,UAAU,CAAC,IACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAElD,aAAY;;;;;;kDAEd,6WAAC,6HAAA,CAAA,WAAQ;wCACP,OAAO,SAAS,WAAW,IAAI;wCAC/B,UAAU,CAAC,IACT,YAAY;gDAAE,GAAG,QAAQ;gDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAEzD,aAAY;wCACZ,MAAM;;;;;;kDAER,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,2HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAS;;kEACzB,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6WAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;oDACP,qBAAqB;oDACrB,YAAY,CAAC;gDACf;;kEAEA,6WAAC,gRAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;uCAMpC,eAAe;0CACf,6WAAC;0CACC,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAG,WAAU;8DACX,SAAS,IAAI;;;;;;gDAEf,SAAS,WAAW,kBACnB,6WAAC;oDAAE,WAAU;8DACV,SAAS,WAAW;;;;;;8DAGzB,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAChC,SAAS,IAAI;;;;;;wDAEf,CAAC,SAAS,QAAQ,kBACjB,6WAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAU;;;;;;sEAIjD,6WAAC;4DAAK,WAAU;;gEACb,SAAS,YAAY,IAAI;gEAAE;;;;;;;;;;;;;;;;;;;sDAKlC,6WAAC,qIAAA,CAAA,eAAY;;8DACX,6WAAC,qIAAA,CAAA,sBAAmB;oDAAC,OAAO;8DAC1B,cAAA,6WAAC,2HAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,6WAAC,oSAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAG9B,6WAAC,qIAAA,CAAA,sBAAmB;oDAAC,OAAM;;sEACzB,6WAAC,qIAAA,CAAA,mBAAgB;4DACf,SAAS;gEACP,qBAAqB,SAAS,GAAG;gEACjC,YAAY;4DACd;;8EAEA,6WAAC,+RAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,6WAAC,qIAAA,CAAA,mBAAgB;;8EACf,6WAAC,oRAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGlC,6WAAC,qIAAA,CAAA,mBAAgB;;8EACf,6WAAC,sRAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,6WAAC,qIAAA,CAAA,wBAAqB;;;;;sEACtB,6WAAC,qIAAA,CAAA,mBAAgB;4DACf,SAAS,IAAM,qBAAqB,SAAS,GAAG;4DAChD,WAAU;;8EAEV,6WAAC,8RAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAnG5C,SAAS,GAAG;;;;;;;;;;;;;;;;AAkH/B", "debugId": null}}, {"offset": {"line": 8237, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative h-2 w-full overflow-hidden rounded-full bg-primary/20\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n))\r\nProgress.displayName = ProgressPrimitive.Root.displayName\r\n\r\nexport { Progress }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6WAAC,8QAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,8QAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,8QAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 8276, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,6QAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,6QAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}]}