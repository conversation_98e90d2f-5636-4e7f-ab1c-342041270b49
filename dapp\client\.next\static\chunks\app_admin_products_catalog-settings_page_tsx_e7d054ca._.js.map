{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/app/admin/products/catalog-settings/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\n\r\nimport {\r\n  Activity,\r\n  ArrowRight,\r\n  BarChart3,\r\n  Calendar,\r\n  Database,\r\n  Download,\r\n  Filter,\r\n  Hammer,\r\n  Layers,\r\n  LayoutGrid,\r\n  Package,\r\n  Palette,\r\n  Plus,\r\n  Search,\r\n  Settings,\r\n  Sparkles,\r\n  Star,\r\n  Tag,\r\n  Target,\r\n  TrendingUp,\r\n  Upload,\r\n  Users,\r\n  Zap,\r\n} from \"lucide-react\";\r\nimport { useRouter, useSearchParams } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\n\r\nimport { PageHeaderWrapper } from \"@/components/common/PageHeaderWrapper\";\r\nimport { BrandManagerEnhanced } from \"@/components/pages/brands/BrandManagerEnhanced\";\r\nimport { ColorManagerEnhanced } from \"@/components/pages/colors/ColorManagerEnhanced\";\r\nimport { CategoryManagerEnhanced } from \"@/components/pages/management/CategoryManagerEnhanced\";\r\nimport { MaterialManagerEnhanced } from \"@/components/pages/materials/MaterialManagerEnhanced\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { <PERSON>, CardContent, <PERSON><PERSON>ead<PERSON>, <PERSON>Tit<PERSON> } from \"@/components/ui/card\";\r\nimport { Progress } from \"@/components/ui/progress\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { useBrands } from \"@/hooks/useBrands\";\r\nimport { useCategories } from \"@/hooks/useCategories\";\r\nimport { useColors } from \"@/hooks/useColors\";\r\nimport { useMaterials } from \"@/hooks/useMaterials\";\r\n\r\nexport default function CatalogSettings() {\r\n  const [activeTab, setActiveTab] = useState(\"overview\");\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n\r\n  // Real data hooks\r\n  const { categories, loading: categoriesLoading } = useCategories();\r\n  const { brands, loading: brandsLoading } = useBrands();\r\n  const { materials, loading: materialsLoading } = useMaterials();\r\n  const { colors, loading: colorsLoading } = useColors();\r\n\r\n  // Handle URL parameters for direct tab navigation\r\n  useEffect(() => {\r\n    const tab = searchParams.get(\"tab\");\r\n    if (\r\n      tab &&\r\n      [\"overview\", \"categories\", \"brands\", \"materials\", \"colors\"].includes(tab)\r\n    ) {\r\n      setActiveTab(tab);\r\n    }\r\n  }, [searchParams]);\r\n\r\n  // Function to handle tab changes and update URL\r\n  const handleTabChange = (newTab: string) => {\r\n    setActiveTab(newTab);\r\n    // Update URL with new tab parameter\r\n    const newSearchParams = new URLSearchParams(searchParams.toString());\r\n    newSearchParams.set(\"tab\", newTab);\r\n    router.push(\r\n      `/admin/products/catalog-settings?${newSearchParams.toString()}`\r\n    );\r\n  };\r\n\r\n  // Calculate real statistics\r\n  const totalCategories = categories.length;\r\n  const activeCategories = categories.filter((c) => c.isActive).length;\r\n  const totalBrands = brands.length;\r\n  const activeBrands = brands.filter((b) => b.isActive).length;\r\n  const totalMaterials = materials.length;\r\n  const activeMaterials = materials.filter((m) => m.isActive).length;\r\n  const totalColors = colors.length;\r\n  const activeColors = colors.filter((c) => c.isActive).length;\r\n\r\n  // Calculate total products across all entities\r\n  const totalProducts =\r\n    [\r\n      ...categories.map((c) => c.productCount || 0),\r\n      ...brands.map((b) => b.productCount || 0),\r\n      ...materials.map((m) => m.productCount || 0),\r\n      ...colors.map((c) => c.productCount || 0),\r\n    ].reduce((sum, count) => sum + count, 0) / 4; // Divide by 4 to avoid counting same products multiple times\r\n\r\n  // Real statistics with dynamic data\r\n  const overallStats = [\r\n    {\r\n      label: \"Total Categories\",\r\n      value: totalCategories.toString(),\r\n      active: activeCategories,\r\n      percentage:\r\n        totalCategories > 0\r\n          ? Math.round((activeCategories / totalCategories) * 100)\r\n          : 0,\r\n      icon: LayoutGrid,\r\n      color: \"blue\",\r\n      description: \"Product categories\",\r\n    },\r\n    {\r\n      label: \"Total Brands\",\r\n      value: totalBrands.toString(),\r\n      active: activeBrands,\r\n      percentage:\r\n        totalBrands > 0 ? Math.round((activeBrands / totalBrands) * 100) : 0,\r\n      icon: Tag,\r\n      color: \"green\",\r\n      description: \"Brand partners\",\r\n    },\r\n    {\r\n      label: \"Total Materials\",\r\n      value: totalMaterials.toString(),\r\n      active: activeMaterials,\r\n      percentage:\r\n        totalMaterials > 0\r\n          ? Math.round((activeMaterials / totalMaterials) * 100)\r\n          : 0,\r\n      icon: Hammer,\r\n      color: \"purple\",\r\n      description: \"Material types\",\r\n    },\r\n    {\r\n      label: \"Total Colors\",\r\n      value: totalColors.toString(),\r\n      active: activeColors,\r\n      percentage:\r\n        totalColors > 0 ? Math.round((activeColors / totalColors) * 100) : 0,\r\n      icon: Palette,\r\n      color: \"pink\",\r\n      description: \"Color options\",\r\n    },\r\n  ];\r\n\r\n  const quickActions = [\r\n    {\r\n      label: \"Import Data\",\r\n      icon: Upload,\r\n      action: () => {\r\n        // Create file input for CSV/JSON import\r\n        const input = document.createElement(\"input\");\r\n        input.type = \"file\";\r\n        input.accept = \".csv,.json\";\r\n        input.onchange = (e) => {\r\n          const file = (e.target as HTMLInputElement).files?.[0];\r\n          if (file) {\r\n            toast.success(`Importing ${file.name}...`);\r\n            // TODO: Implement actual import logic\r\n          }\r\n        };\r\n        input.click();\r\n      },\r\n    },\r\n    {\r\n      label: \"Export All\",\r\n      icon: Download,\r\n      action: () => {\r\n        // Generate export data\r\n        const exportData = {\r\n          categories: categories.map((c) => ({\r\n            name: c.name,\r\n            description: c.description,\r\n            isActive: c.isActive,\r\n          })),\r\n          brands: brands.map((b) => ({\r\n            name: b.name,\r\n            description: b.description,\r\n            isActive: b.isActive,\r\n          })),\r\n          materials: materials.map((m) => ({\r\n            name: m.name,\r\n            description: m.description,\r\n            type: m.type,\r\n            isActive: m.isActive,\r\n          })),\r\n          colors: colors.map((c) => ({\r\n            name: c.name,\r\n            hexCode: c.hexCode,\r\n            family: c.family,\r\n            isActive: c.isActive,\r\n          })),\r\n        };\r\n\r\n        const blob = new Blob([JSON.stringify(exportData, null, 2)], {\r\n          type: \"application/json\",\r\n        });\r\n        const url = URL.createObjectURL(blob);\r\n        const a = document.createElement(\"a\");\r\n        a.href = url;\r\n        a.download = `catalog-export-${new Date().toISOString().split(\"T\")[0]}.json`;\r\n        a.click();\r\n        URL.revokeObjectURL(url);\r\n        toast.success(\"Catalog data exported successfully!\");\r\n      },\r\n    },\r\n    {\r\n      label: \"Catalog Analytics\",\r\n      icon: BarChart3,\r\n      action: () => router.push(\"/admin/analytics/catalog\"),\r\n    },\r\n    {\r\n      label: \"Bulk Operations\",\r\n      icon: Settings,\r\n      action: () => toast.info(\"Bulk operations panel coming soon!\"),\r\n    },\r\n  ];\r\n\r\n  // Generate insights from real data\r\n  const catalogInsights = [\r\n    {\r\n      title: \"Most Popular Categories\",\r\n      data: categories\r\n        .sort((a, b) => (b.productCount || 0) - (a.productCount || 0))\r\n        .slice(0, 3)\r\n        .map((c) => ({ name: c.name, count: c.productCount || 0 })),\r\n      icon: TrendingUp,\r\n      color: \"blue\",\r\n    },\r\n    {\r\n      title: \"Top Brands\",\r\n      data: brands\r\n        .sort((a, b) => (b.productCount || 0) - (a.productCount || 0))\r\n        .slice(0, 3)\r\n        .map((b) => ({ name: b.name, count: b.productCount || 0 })),\r\n      icon: Star,\r\n      color: \"green\",\r\n    },\r\n    {\r\n      title: \"Popular Materials\",\r\n      data: materials\r\n        .sort((a, b) => (b.productCount || 0) - (a.productCount || 0))\r\n        .slice(0, 3)\r\n        .map((m) => ({ name: m.name, count: m.productCount || 0 })),\r\n      icon: Hammer,\r\n      color: \"purple\",\r\n    },\r\n    {\r\n      title: \"Trending Colors\",\r\n      data: colors\r\n        .sort((a, b) => (b.productCount || 0) - (a.productCount || 0))\r\n        .slice(0, 3)\r\n        .map((c) => ({\r\n          name: c.name,\r\n          count: c.productCount || 0,\r\n          hex: c.hexCode,\r\n        })),\r\n      icon: Palette,\r\n      color: \"pink\",\r\n    },\r\n  ];\r\n\r\n  // Catalog health metrics\r\n  const catalogHealth = {\r\n    completeness: Math.round(\r\n      ((activeCategories + activeBrands + activeMaterials + activeColors) /\r\n        Math.max(\r\n          totalCategories + totalBrands + totalMaterials + totalColors,\r\n          1\r\n        )) *\r\n        100\r\n    ),\r\n    diversity: Math.min(\r\n      100,\r\n      Math.round(\r\n        ((totalCategories * totalBrands * totalMaterials * totalColors) /\r\n          1000) *\r\n          100\r\n      )\r\n    ),\r\n    activity: Math.round(\r\n      (totalProducts /\r\n        Math.max(\r\n          totalCategories + totalBrands + totalMaterials + totalColors,\r\n          1\r\n        )) *\r\n        10\r\n    ),\r\n    consistency: Math.round(\r\n      ((categories.filter((c) => c.description).length /\r\n        Math.max(totalCategories, 1) +\r\n        brands.filter((b) => b.description).length / Math.max(totalBrands, 1) +\r\n        materials.filter((m) => m.description).length /\r\n          Math.max(totalMaterials, 1) +\r\n        colors.filter((c) => c.description).length / Math.max(totalColors, 1)) /\r\n        4) *\r\n        100\r\n    ),\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <PageHeaderWrapper\r\n        title=\"Catalog Settings\"\r\n        description=\"Manage categories, brands, and materials for your product catalog in one unified interface\"\r\n      >\r\n        <div className=\"flex gap-2\">\r\n          {quickActions.map((action, index) => (\r\n            <Button\r\n              key={index}\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={action.action}\r\n              className=\"hidden sm:flex\"\r\n            >\r\n              <action.icon className=\"mr-2 h-4 w-4\" />\r\n              {action.label}\r\n            </Button>\r\n          ))}\r\n          <Button size=\"sm\">\r\n            <Plus className=\"mr-2 h-4 w-4\" />\r\n            Quick Add\r\n          </Button>\r\n        </div>\r\n      </PageHeaderWrapper>\r\n\r\n      <div className=\"container mx-auto mt-6 space-y-6\">\r\n        <Tabs\r\n          value={activeTab}\r\n          onValueChange={handleTabChange}\r\n          className=\"w-full\"\r\n        >\r\n          <TabsList className=\"grid w-full grid-cols-5\">\r\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\r\n            <TabsTrigger value=\"categories\">Categories</TabsTrigger>\r\n            <TabsTrigger value=\"brands\">Brands</TabsTrigger>\r\n            <TabsTrigger value=\"materials\">Materials</TabsTrigger>\r\n            <TabsTrigger value=\"colors\">Colors</TabsTrigger>\r\n          </TabsList>\r\n\r\n          {/* Overview Tab */}\r\n          <TabsContent value=\"overview\" className=\"space-y-8\">\r\n            {/* Hero Section */}\r\n            <div className=\"relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-8\">\r\n              <div className=\"relative z-10\">\r\n                <div className=\"mb-4 flex items-center gap-3\">\r\n                  <div className=\"rounded-full bg-blue-100 p-3\">\r\n                    <Database className=\"h-8 w-8 text-blue-600\" />\r\n                  </div>\r\n                  <div>\r\n                    <h2 className=\"text-2xl font-bold text-gray-900\">\r\n                      Catalog Overview\r\n                    </h2>\r\n                    <p className=\"text-gray-600\">\r\n                      Manage your entire product catalog ecosystem\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"mt-6 grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\r\n                  <div className=\"rounded-lg border border-white/20 bg-white/70 p-4 backdrop-blur-sm\">\r\n                    <div className=\"mb-2 flex items-center gap-2\">\r\n                      <Package className=\"h-5 w-5 text-blue-600\" />\r\n                      <span className=\"text-sm font-medium text-gray-700\">\r\n                        Total Products\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"text-2xl font-bold text-gray-900\">\r\n                      {Math.round(totalProducts)}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-600\">\r\n                      Across all categories\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"rounded-lg border border-white/20 bg-white/70 p-4 backdrop-blur-sm\">\r\n                    <div className=\"mb-2 flex items-center gap-2\">\r\n                      <Activity className=\"h-5 w-5 text-green-600\" />\r\n                      <span className=\"text-sm font-medium text-gray-700\">\r\n                        Active Items\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"text-2xl font-bold text-gray-900\">\r\n                      {activeCategories +\r\n                        activeBrands +\r\n                        activeMaterials +\r\n                        activeColors}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-600\">Ready for use</div>\r\n                  </div>\r\n                  <div className=\"rounded-lg border border-white/20 bg-white/70 p-4 backdrop-blur-sm\">\r\n                    <div className=\"mb-2 flex items-center gap-2\">\r\n                      <Target className=\"h-5 w-5 text-purple-600\" />\r\n                      <span className=\"text-sm font-medium text-gray-700\">\r\n                        Completion\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"text-2xl font-bold text-gray-900\">\r\n                      {Math.round(\r\n                        ((activeCategories +\r\n                          activeBrands +\r\n                          activeMaterials +\r\n                          activeColors) /\r\n                          (totalCategories +\r\n                            totalBrands +\r\n                            totalMaterials +\r\n                            totalColors)) *\r\n                          100\r\n                      ) || 0}\r\n                      %\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-600\">Catalog setup</div>\r\n                  </div>\r\n                  <div className=\"rounded-lg border border-white/20 bg-white/70 p-4 backdrop-blur-sm\">\r\n                    <div className=\"mb-2 flex items-center gap-2\">\r\n                      <Zap className=\"h-5 w-5 text-orange-600\" />\r\n                      <span className=\"text-sm font-medium text-gray-700\">\r\n                        Health Score\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"text-2xl font-bold text-gray-900\">\r\n                      {totalCategories > 0 &&\r\n                      totalBrands > 0 &&\r\n                      totalMaterials > 0 &&\r\n                      totalColors > 0\r\n                        ? \"98%\"\r\n                        : \"75%\"}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-600\">System health</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div className=\"absolute right-0 top-0 h-64 w-64 rounded-full bg-gradient-to-br from-blue-200/30 to-purple-200/30 blur-3xl\"></div>\r\n            </div>\r\n\r\n            {/* Detailed Stats */}\r\n            <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-4\">\r\n              {overallStats.map((stat, index) => (\r\n                <Card\r\n                  key={index}\r\n                  className=\"group relative overflow-hidden transition-all duration-300 hover:shadow-lg\"\r\n                >\r\n                  <CardContent className=\"p-6\">\r\n                    <div className=\"flex items-center gap-4\">\r\n                      <div\r\n                        className={`rounded-xl p-3 bg-${stat.color}-100 group-hover:bg-${stat.color}-200 transition-colors`}\r\n                      >\r\n                        <stat.icon\r\n                          className={`h-6 w-6 text-${stat.color}-600`}\r\n                        />\r\n                      </div>\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"mb-1 flex items-center justify-between\">\r\n                          <p className=\"text-sm font-medium text-gray-600\">\r\n                            {stat.label}\r\n                          </p>\r\n                          <Badge variant=\"outline\" className=\"text-xs\">\r\n                            {stat.active} active\r\n                          </Badge>\r\n                        </div>\r\n                        <div className=\"mb-2 text-3xl font-bold text-gray-900\">\r\n                          {stat.value}\r\n                        </div>\r\n                        <div className=\"space-y-2\">\r\n                          <div className=\"flex items-center justify-between text-xs\">\r\n                            <span className=\"text-gray-500\">\r\n                              {stat.description}\r\n                            </span>\r\n                            <span className=\"font-medium text-gray-700\">\r\n                              {stat.percentage}%\r\n                            </span>\r\n                          </div>\r\n                          <Progress value={stat.percentage} className=\"h-2\" />\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </CardContent>\r\n                  <div\r\n                    className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-${stat.color}-400 to-${stat.color}-600`}\r\n                  ></div>\r\n                </Card>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Catalog Health Dashboard */}\r\n            <div className=\"grid gap-6 md:grid-cols-2\">\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2\">\r\n                    <Activity className=\"h-5 w-5 text-green-600\" />\r\n                    Catalog Health\r\n                  </CardTitle>\r\n                  <p className=\"text-sm text-gray-600\">\r\n                    Monitor the overall health and completeness of your catalog\r\n                  </p>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-4\">\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm font-medium text-gray-700\">\r\n                        Completeness\r\n                      </span>\r\n                      <span className=\"text-sm font-bold text-gray-900\">\r\n                        {catalogHealth.completeness}%\r\n                      </span>\r\n                    </div>\r\n                    <Progress\r\n                      value={catalogHealth.completeness}\r\n                      className=\"h-2\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm font-medium text-gray-700\">\r\n                        Diversity\r\n                      </span>\r\n                      <span className=\"text-sm font-bold text-gray-900\">\r\n                        {catalogHealth.diversity}%\r\n                      </span>\r\n                    </div>\r\n                    <Progress value={catalogHealth.diversity} className=\"h-2\" />\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm font-medium text-gray-700\">\r\n                        Activity Level\r\n                      </span>\r\n                      <span className=\"text-sm font-bold text-gray-900\">\r\n                        {Math.min(100, catalogHealth.activity)}%\r\n                      </span>\r\n                    </div>\r\n                    <Progress\r\n                      value={Math.min(100, catalogHealth.activity)}\r\n                      className=\"h-2\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm font-medium text-gray-700\">\r\n                        Data Consistency\r\n                      </span>\r\n                      <span className=\"text-sm font-bold text-gray-900\">\r\n                        {catalogHealth.consistency}%\r\n                      </span>\r\n                    </div>\r\n                    <Progress\r\n                      value={catalogHealth.consistency}\r\n                      className=\"h-2\"\r\n                    />\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2\">\r\n                    <BarChart3 className=\"h-5 w-5 text-purple-600\" />\r\n                    Catalog Insights\r\n                  </CardTitle>\r\n                  <p className=\"text-sm text-gray-600\">\r\n                    Top performing items in your catalog\r\n                  </p>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-4\">\r\n                  {catalogInsights.map((insight, index) => (\r\n                    <div key={index} className=\"space-y-2\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <insight.icon\r\n                          className={`h-4 w-4 text-${insight.color}-600`}\r\n                        />\r\n                        <span className=\"text-sm font-medium text-gray-700\">\r\n                          {insight.title}\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"space-y-1\">\r\n                        {insight.data.map((item, idx) => (\r\n                          <div\r\n                            key={idx}\r\n                            className=\"flex items-center justify-between text-xs\"\r\n                          >\r\n                            <div className=\"flex items-center gap-2\">\r\n                              {insight.title === \"Trending Colors\" &&\r\n                                item.hex && (\r\n                                  <div\r\n                                    className=\"h-3 w-3 rounded-full border border-gray-300\"\r\n                                    style={{ backgroundColor: item.hex }}\r\n                                  />\r\n                                )}\r\n                              <span className=\"text-gray-600\">{item.name}</span>\r\n                            </div>\r\n                            <Badge variant=\"outline\" className=\"text-xs\">\r\n                              {item.count} products\r\n                            </Badge>\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n\r\n            {/* Quick Actions Grid */}\r\n            <div className=\"grid gap-4 md:grid-cols-4\">\r\n              <Card\r\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\r\n                onClick={() => handleTabChange(\"categories\")}\r\n              >\r\n                <CardContent className=\"p-6 text-center\">\r\n                  <LayoutGrid className=\"mx-auto mb-3 h-8 w-8 text-blue-600\" />\r\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\r\n                    Manage Categories\r\n                  </h3>\r\n                  <p className=\"mb-3 text-sm text-gray-600\">\r\n                    Organize your product catalog\r\n                  </p>\r\n                  <div className=\"flex items-center justify-center text-sm text-blue-600\">\r\n                    <span>Go to Categories</span>\r\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              <Card\r\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\r\n                onClick={() => handleTabChange(\"brands\")}\r\n              >\r\n                <CardContent className=\"p-6 text-center\">\r\n                  <Tag className=\"mx-auto mb-3 h-8 w-8 text-green-600\" />\r\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\r\n                    Manage Brands\r\n                  </h3>\r\n                  <p className=\"mb-3 text-sm text-gray-600\">\r\n                    Add and organize product brands\r\n                  </p>\r\n                  <div className=\"flex items-center justify-center text-sm text-green-600\">\r\n                    <span>Go to Brands</span>\r\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              <Card\r\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\r\n                onClick={() => handleTabChange(\"materials\")}\r\n              >\r\n                <CardContent className=\"p-6 text-center\">\r\n                  <Hammer className=\"mx-auto mb-3 h-8 w-8 text-purple-600\" />\r\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\r\n                    Manage Materials\r\n                  </h3>\r\n                  <p className=\"mb-3 text-sm text-gray-600\">\r\n                    Define product materials\r\n                  </p>\r\n                  <div className=\"flex items-center justify-center text-sm text-purple-600\">\r\n                    <span>Go to Materials</span>\r\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              <Card\r\n                className=\"cursor-pointer transition-shadow hover:shadow-md\"\r\n                onClick={() => handleTabChange(\"colors\")}\r\n              >\r\n                <CardContent className=\"p-6 text-center\">\r\n                  <Palette className=\"mx-auto mb-3 h-8 w-8 text-pink-600\" />\r\n                  <h3 className=\"mb-2 font-semibold text-gray-900\">\r\n                    Manage Colors\r\n                  </h3>\r\n                  <p className=\"mb-3 text-sm text-gray-600\">\r\n                    Define product color options\r\n                  </p>\r\n                  <div className=\"flex items-center justify-center text-sm text-pink-600\">\r\n                    <span>Go to Colors</span>\r\n                    <ArrowRight className=\"ml-1 h-4 w-4\" />\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          </TabsContent>\r\n\r\n          {/* Categories Tab */}\r\n          <TabsContent value=\"categories\" className=\"space-y-6\">\r\n            <div className=\"mb-6 rounded-lg border bg-gradient-to-r from-blue-50 to-indigo-50 p-4\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"rounded-full bg-blue-100 p-2\">\r\n                  <LayoutGrid className=\"h-5 w-5 text-blue-600\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"font-semibold text-gray-900\">\r\n                    Category Management\r\n                  </h3>\r\n                  <p className=\"text-sm text-gray-600\">\r\n                    Organize your products with categories and subcategories for\r\n                    better navigation\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <CategoryManagerEnhanced />\r\n          </TabsContent>\r\n\r\n          {/* Brands Tab */}\r\n          <TabsContent value=\"brands\" className=\"space-y-6\">\r\n            <div className=\"mb-6 rounded-lg border bg-gradient-to-r from-green-50 to-emerald-50 p-4\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"rounded-full bg-green-100 p-2\">\r\n                  <Tag className=\"h-5 w-5 text-green-600\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"font-semibold text-gray-900\">\r\n                    Brand Management\r\n                  </h3>\r\n                  <p className=\"text-sm text-gray-600\">\r\n                    Create, organize, and manage product brands for your catalog\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <BrandManagerEnhanced />\r\n          </TabsContent>\r\n\r\n          {/* Materials Tab */}\r\n          <TabsContent value=\"materials\" className=\"space-y-6\">\r\n            <div className=\"mb-6 rounded-lg border bg-gradient-to-r from-purple-50 to-violet-50 p-4\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"rounded-full bg-purple-100 p-2\">\r\n                  <Hammer className=\"h-5 w-5 text-purple-600\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"font-semibold text-gray-900\">\r\n                    Material Management\r\n                  </h3>\r\n                  <p className=\"text-sm text-gray-600\">\r\n                    Define and manage materials used in your products for better\r\n                    specifications\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <MaterialManagerEnhanced />\r\n          </TabsContent>\r\n\r\n          {/* Colors Tab */}\r\n          <TabsContent value=\"colors\" className=\"space-y-6\">\r\n            <div className=\"mb-6 rounded-lg border bg-gradient-to-r from-pink-50 to-rose-50 p-4\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"rounded-full bg-pink-100 p-2\">\r\n                  <Palette className=\"h-5 w-5 text-pink-600\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"font-semibold text-gray-900\">\r\n                    Color Management\r\n                  </h3>\r\n                  <p className=\"text-sm text-gray-600\">\r\n                    Create and manage color options for your products to enhance\r\n                    customer choice and visual appeal\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <ColorManagerEnhanced />\r\n          </TabsContent>\r\n        </Tabs>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA7CA;;;;;;;;;;;;;;;;;;;AA+Ce,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,oQAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IAEvB,kBAAkB;IAClB,MAAM,EAAE,UAAU,EAAE,SAAS,iBAAiB,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;IAC/D,MAAM,EAAE,MAAM,EAAE,SAAS,aAAa,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,SAAS,gBAAgB,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD;IAC5D,MAAM,EAAE,MAAM,EAAE,SAAS,aAAa,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD;IAEnD,kDAAkD;IAClD,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,MAAM,aAAa,GAAG,CAAC;YAC7B,IACE,OACA;gBAAC;gBAAY;gBAAc;gBAAU;gBAAa;aAAS,CAAC,QAAQ,CAAC,MACrE;gBACA,aAAa;YACf;QACF;oCAAG;QAAC;KAAa;IAEjB,gDAAgD;IAChD,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,oCAAoC;QACpC,MAAM,kBAAkB,IAAI,gBAAgB,aAAa,QAAQ;QACjE,gBAAgB,GAAG,CAAC,OAAO;QAC3B,OAAO,IAAI,CACT,CAAC,iCAAiC,EAAE,gBAAgB,QAAQ,IAAI;IAEpE;IAEA,4BAA4B;IAC5B,MAAM,kBAAkB,WAAW,MAAM;IACzC,MAAM,mBAAmB,WAAW,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;IACpE,MAAM,cAAc,OAAO,MAAM;IACjC,MAAM,eAAe,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;IAC5D,MAAM,iBAAiB,UAAU,MAAM;IACvC,MAAM,kBAAkB,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;IAClE,MAAM,cAAc,OAAO,MAAM;IACjC,MAAM,eAAe,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,MAAM;IAE5D,+CAA+C;IAC/C,MAAM,gBACJ;WACK,WAAW,GAAG,CAAC,CAAC,IAAM,EAAE,YAAY,IAAI;WACxC,OAAO,GAAG,CAAC,CAAC,IAAM,EAAE,YAAY,IAAI;WACpC,UAAU,GAAG,CAAC,CAAC,IAAM,EAAE,YAAY,IAAI;WACvC,OAAO,GAAG,CAAC,CAAC,IAAM,EAAE,YAAY,IAAI;KACxC,CAAC,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,OAAO,KAAK,GAAG,6DAA6D;IAE7G,oCAAoC;IACpC,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO,gBAAgB,QAAQ;YAC/B,QAAQ;YACR,YACE,kBAAkB,IACd,KAAK,KAAK,CAAC,AAAC,mBAAmB,kBAAmB,OAClD;YACN,MAAM,ySAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,YAAY,QAAQ;YAC3B,QAAQ;YACR,YACE,cAAc,IAAI,KAAK,KAAK,CAAC,AAAC,eAAe,cAAe,OAAO;YACrE,MAAM,uRAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,eAAe,QAAQ;YAC9B,QAAQ;YACR,YACE,iBAAiB,IACb,KAAK,KAAK,CAAC,AAAC,kBAAkB,iBAAkB,OAChD;YACN,MAAM,6RAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO,YAAY,QAAQ;YAC3B,QAAQ;YACR,YACE,cAAc,IAAI,KAAK,KAAK,CAAC,AAAC,eAAe,cAAe,OAAO;YACrE,MAAM,+RAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,eAAe;QACnB;YACE,OAAO;YACP,MAAM,6RAAA,CAAA,SAAM;YACZ,QAAQ;gBACN,wCAAwC;gBACxC,MAAM,QAAQ,SAAS,aAAa,CAAC;gBACrC,MAAM,IAAI,GAAG;gBACb,MAAM,MAAM,GAAG;gBACf,MAAM,QAAQ,GAAG,CAAC;oBAChB,MAAM,OAAO,AAAC,EAAE,MAAM,CAAsB,KAAK,EAAE,CAAC,EAAE;oBACtD,IAAI,MAAM;wBACR,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC;oBACzC,sCAAsC;oBACxC;gBACF;gBACA,MAAM,KAAK;YACb;QACF;QACA;YACE,OAAO;YACP,MAAM,iSAAA,CAAA,WAAQ;YACd,QAAQ;gBACN,uBAAuB;gBACvB,MAAM,aAAa;oBACjB,YAAY,WAAW,GAAG,CAAC,CAAC,IAAM,CAAC;4BACjC,MAAM,EAAE,IAAI;4BACZ,aAAa,EAAE,WAAW;4BAC1B,UAAU,EAAE,QAAQ;wBACtB,CAAC;oBACD,QAAQ,OAAO,GAAG,CAAC,CAAC,IAAM,CAAC;4BACzB,MAAM,EAAE,IAAI;4BACZ,aAAa,EAAE,WAAW;4BAC1B,UAAU,EAAE,QAAQ;wBACtB,CAAC;oBACD,WAAW,UAAU,GAAG,CAAC,CAAC,IAAM,CAAC;4BAC/B,MAAM,EAAE,IAAI;4BACZ,aAAa,EAAE,WAAW;4BAC1B,MAAM,EAAE,IAAI;4BACZ,UAAU,EAAE,QAAQ;wBACtB,CAAC;oBACD,QAAQ,OAAO,GAAG,CAAC,CAAC,IAAM,CAAC;4BACzB,MAAM,EAAE,IAAI;4BACZ,SAAS,EAAE,OAAO;4BAClB,QAAQ,EAAE,MAAM;4BAChB,UAAU,EAAE,QAAQ;wBACtB,CAAC;gBACH;gBAEA,MAAM,OAAO,IAAI,KAAK;oBAAC,KAAK,SAAS,CAAC,YAAY,MAAM;iBAAG,EAAE;oBAC3D,MAAM;gBACR;gBACA,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,eAAe,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;gBAC5E,EAAE,KAAK;gBACP,IAAI,eAAe,CAAC;gBACpB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF;QACA;YACE,OAAO;YACP,MAAM,ySAAA,CAAA,YAAS;YACf,QAAQ,IAAM,OAAO,IAAI,CAAC;QAC5B;QACA;YACE,OAAO;YACP,MAAM,iSAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QAC3B;KACD;IAED,mCAAmC;IACnC,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,MAAM,WACH,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,GAC3D,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,IAAM,CAAC;oBAAE,MAAM,EAAE,IAAI;oBAAE,OAAO,EAAE,YAAY,IAAI;gBAAE,CAAC;YAC3D,MAAM,ySAAA,CAAA,aAAU;YAChB,OAAO;QACT;QACA;YACE,OAAO;YACP,MAAM,OACH,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,GAC3D,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,IAAM,CAAC;oBAAE,MAAM,EAAE,IAAI;oBAAE,OAAO,EAAE,YAAY,IAAI;gBAAE,CAAC;YAC3D,MAAM,yRAAA,CAAA,OAAI;YACV,OAAO;QACT;QACA;YACE,OAAO;YACP,MAAM,UACH,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,GAC3D,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,IAAM,CAAC;oBAAE,MAAM,EAAE,IAAI;oBAAE,OAAO,EAAE,YAAY,IAAI;gBAAE,CAAC;YAC3D,MAAM,6RAAA,CAAA,SAAM;YACZ,OAAO;QACT;QACA;YACE,OAAO;YACP,MAAM,OACH,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,GAC3D,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,IAAM,CAAC;oBACX,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,YAAY,IAAI;oBACzB,KAAK,EAAE,OAAO;gBAChB,CAAC;YACH,MAAM,+RAAA,CAAA,UAAO;YACb,OAAO;QACT;KACD;IAED,yBAAyB;IACzB,MAAM,gBAAgB;QACpB,cAAc,KAAK,KAAK,CACtB,AAAC,CAAC,mBAAmB,eAAe,kBAAkB,YAAY,IAChE,KAAK,GAAG,CACN,kBAAkB,cAAc,iBAAiB,aACjD,KAEF;QAEJ,WAAW,KAAK,GAAG,CACjB,KACA,KAAK,KAAK,CACR,AAAE,kBAAkB,cAAc,iBAAiB,cACjD,OACA;QAGN,UAAU,KAAK,KAAK,CAClB,AAAC,gBACC,KAAK,GAAG,CACN,kBAAkB,cAAc,iBAAiB,aACjD,KAEF;QAEJ,aAAa,KAAK,KAAK,CACrB,AAAC,CAAC,WAAW,MAAM,CAAC,CAAC,IAAM,EAAE,WAAW,EAAE,MAAM,GAC9C,KAAK,GAAG,CAAC,iBAAiB,KAC1B,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,WAAW,EAAE,MAAM,GAAG,KAAK,GAAG,CAAC,aAAa,KACnE,UAAU,MAAM,CAAC,CAAC,IAAM,EAAE,WAAW,EAAE,MAAM,GAC3C,KAAK,GAAG,CAAC,gBAAgB,KAC3B,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,WAAW,EAAE,MAAM,GAAG,KAAK,GAAG,CAAC,aAAa,EAAE,IACrE,IACA;IAEN;IAEA,qBACE;;0BACE,4TAAC,6IAAA,CAAA,oBAAiB;gBAChB,OAAM;gBACN,aAAY;0BAEZ,cAAA,4TAAC;oBAAI,WAAU;;wBACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,4TAAC,8HAAA,CAAA,SAAM;gCAEL,SAAQ;gCACR,MAAK;gCACL,SAAS,OAAO,MAAM;gCACtB,WAAU;;kDAEV,4TAAC,OAAO,IAAI;wCAAC,WAAU;;;;;;oCACtB,OAAO,KAAK;;+BAPR;;;;;sCAUT,4TAAC,8HAAA,CAAA,SAAM;4BAAC,MAAK;;8CACX,4TAAC,yRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAMvC,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,4HAAA,CAAA,OAAI;oBACH,OAAO;oBACP,eAAe;oBACf,WAAU;;sCAEV,4TAAC,4HAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,4TAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,4TAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;8CAAa;;;;;;8CAChC,4TAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;8CAC5B,4TAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;8CAC/B,4TAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;;;;;;;sCAI9B,4TAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;;8CAEtC,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;sEACb,cAAA,4TAAC,iSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,4TAAC;;8EACC,4TAAC;oEAAG,WAAU;8EAAmC;;;;;;8EAGjD,4TAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;8DAKjC,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC,+RAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;sFACnB,4TAAC;4EAAK,WAAU;sFAAoC;;;;;;;;;;;;8EAItD,4TAAC;oEAAI,WAAU;8EACZ,KAAK,KAAK,CAAC;;;;;;8EAEd,4TAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAIzC,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC,iSAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,4TAAC;4EAAK,WAAU;sFAAoC;;;;;;;;;;;;8EAItD,4TAAC;oEAAI,WAAU;8EACZ,mBACC,eACA,kBACA;;;;;;8EAEJ,4TAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC,6RAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,4TAAC;4EAAK,WAAU;sFAAoC;;;;;;;;;;;;8EAItD,4TAAC;oEAAI,WAAU;;wEACZ,KAAK,KAAK,CACT,AAAC,CAAC,mBACA,eACA,kBACA,YAAY,IACZ,CAAC,kBACC,cACA,iBACA,WAAW,IACb,QACC;wEAAE;;;;;;;8EAGT,4TAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC,uRAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;sFACf,4TAAC;4EAAK,WAAU;sFAAoC;;;;;;;;;;;;8EAItD,4TAAC;oEAAI,WAAU;8EACZ,kBAAkB,KACnB,cAAc,KACd,iBAAiB,KACjB,cAAc,IACV,QACA;;;;;;8EAEN,4TAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAI7C,4TAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,4TAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,4TAAC,4HAAA,CAAA,OAAI;4CAEH,WAAU;;8DAEV,4TAAC,4HAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEACC,WAAW,CAAC,kBAAkB,EAAE,KAAK,KAAK,CAAC,oBAAoB,EAAE,KAAK,KAAK,CAAC,sBAAsB,CAAC;0EAEnG,cAAA,4TAAC,KAAK,IAAI;oEACR,WAAW,CAAC,aAAa,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC;;;;;;;;;;;0EAG/C,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;0FACb,4TAAC;gFAAE,WAAU;0FACV,KAAK,KAAK;;;;;;0FAEb,4TAAC,6HAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;;oFAChC,KAAK,MAAM;oFAAC;;;;;;;;;;;;;kFAGjB,4TAAC;wEAAI,WAAU;kFACZ,KAAK,KAAK;;;;;;kFAEb,4TAAC;wEAAI,WAAU;;0FACb,4TAAC;gFAAI,WAAU;;kGACb,4TAAC;wFAAK,WAAU;kGACb,KAAK,WAAW;;;;;;kGAEnB,4TAAC;wFAAK,WAAU;;4FACb,KAAK,UAAU;4FAAC;;;;;;;;;;;;;0FAGrB,4TAAC,gIAAA,CAAA,WAAQ;gFAAC,OAAO,KAAK,UAAU;gFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAKpD,4TAAC;oDACC,WAAW,CAAC,2DAA2D,EAAE,KAAK,KAAK,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC;;;;;;;2CAvC3G;;;;;;;;;;8CA8CX,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,4HAAA,CAAA,OAAI;;8DACH,4TAAC,4HAAA,CAAA,aAAU;;sEACT,4TAAC,4HAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,4TAAC,iSAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAA2B;;;;;;;sEAGjD,4TAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,4TAAC,4HAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAK,WAAU;sFAAoC;;;;;;sFAGpD,4TAAC;4EAAK,WAAU;;gFACb,cAAc,YAAY;gFAAC;;;;;;;;;;;;;8EAGhC,4TAAC,gIAAA,CAAA,WAAQ;oEACP,OAAO,cAAc,YAAY;oEACjC,WAAU;;;;;;;;;;;;sEAId,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAK,WAAU;sFAAoC;;;;;;sFAGpD,4TAAC;4EAAK,WAAU;;gFACb,cAAc,SAAS;gFAAC;;;;;;;;;;;;;8EAG7B,4TAAC,gIAAA,CAAA,WAAQ;oEAAC,OAAO,cAAc,SAAS;oEAAE,WAAU;;;;;;;;;;;;sEAGtD,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAK,WAAU;sFAAoC;;;;;;sFAGpD,4TAAC;4EAAK,WAAU;;gFACb,KAAK,GAAG,CAAC,KAAK,cAAc,QAAQ;gFAAE;;;;;;;;;;;;;8EAG3C,4TAAC,gIAAA,CAAA,WAAQ;oEACP,OAAO,KAAK,GAAG,CAAC,KAAK,cAAc,QAAQ;oEAC3C,WAAU;;;;;;;;;;;;sEAId,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAK,WAAU;sFAAoC;;;;;;sFAGpD,4TAAC;4EAAK,WAAU;;gFACb,cAAc,WAAW;gFAAC;;;;;;;;;;;;;8EAG/B,4TAAC,gIAAA,CAAA,WAAQ;oEACP,OAAO,cAAc,WAAW;oEAChC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAMlB,4TAAC,4HAAA,CAAA,OAAI;;8DACH,4TAAC,4HAAA,CAAA,aAAU;;sEACT,4TAAC,4HAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,4TAAC,ySAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAA4B;;;;;;;sEAGnD,4TAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,4TAAC,4HAAA,CAAA,cAAW;oDAAC,WAAU;8DACpB,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,4TAAC;4DAAgB,WAAU;;8EACzB,4TAAC;oEAAI,WAAU;;sFACb,4TAAC,QAAQ,IAAI;4EACX,WAAW,CAAC,aAAa,EAAE,QAAQ,KAAK,CAAC,IAAI,CAAC;;;;;;sFAEhD,4TAAC;4EAAK,WAAU;sFACb,QAAQ,KAAK;;;;;;;;;;;;8EAGlB,4TAAC;oEAAI,WAAU;8EACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,oBACvB,4TAAC;4EAEC,WAAU;;8FAEV,4TAAC;oFAAI,WAAU;;wFACZ,QAAQ,KAAK,KAAK,qBACjB,KAAK,GAAG,kBACN,4TAAC;4FACC,WAAU;4FACV,OAAO;gGAAE,iBAAiB,KAAK,GAAG;4FAAC;;;;;;sGAGzC,4TAAC;4FAAK,WAAU;sGAAiB,KAAK,IAAI;;;;;;;;;;;;8FAE5C,4TAAC,6HAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;wFAChC,KAAK,KAAK;wFAAC;;;;;;;;2EAdT;;;;;;;;;;;2DAZH;;;;;;;;;;;;;;;;;;;;;;8CAsClB,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,4HAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,gBAAgB;sDAE/B,cAAA,4TAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,4TAAC,ySAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,4TAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,4TAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;0EAAK;;;;;;0EACN,4TAAC,ySAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAK5B,4TAAC,4HAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,gBAAgB;sDAE/B,cAAA,4TAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,4TAAC,uRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,4TAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,4TAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;0EAAK;;;;;;0EACN,4TAAC,ySAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAK5B,4TAAC,4HAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,gBAAgB;sDAE/B,cAAA,4TAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,4TAAC,6RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,4TAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,4TAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;0EAAK;;;;;;0EACN,4TAAC,ySAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAK5B,4TAAC,4HAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,gBAAgB;sDAE/B,cAAA,4TAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,4TAAC,+RAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,4TAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAGjD,4TAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;0EAAK;;;;;;0EACN,4TAAC,ySAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhC,4TAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAa,WAAU;;8CACxC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;0DACb,cAAA,4TAAC,ySAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,4TAAC;;kEACC,4TAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAG5C,4TAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;8CAO3C,4TAAC,gKAAA,CAAA,0BAAuB;;;;;;;;;;;sCAI1B,4TAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;;8CACpC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;0DACb,cAAA,4TAAC,uRAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,4TAAC;;kEACC,4TAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAG5C,4TAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;8CAM3C,4TAAC,yJAAA,CAAA,uBAAoB;;;;;;;;;;;sCAIvB,4TAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;;8CACvC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;0DACb,cAAA,4TAAC,6RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,4TAAC;;kEACC,4TAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAG5C,4TAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;8CAO3C,4TAAC,+JAAA,CAAA,0BAAuB;;;;;;;;;;;sCAI1B,4TAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;;8CACpC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;0DACb,cAAA,4TAAC,+RAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,4TAAC;;kEACC,4TAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAG5C,4TAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;8CAO3C,4TAAC,yJAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;;;;;;;;;AAMjC;GArtBwB;;QAGD,oQAAA,CAAA,kBAAe;QACrB,oQAAA,CAAA,YAAS;QAG2B,yHAAA,CAAA,gBAAa;QACrB,qHAAA,CAAA,YAAS;QACH,wHAAA,CAAA,eAAY;QAClB,qHAAA,CAAA,YAAS;;;KAV9B", "debugId": null}}]}