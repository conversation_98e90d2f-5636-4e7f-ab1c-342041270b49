{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, orientation = \"horizontal\", decorative = true, ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      ref={ref}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"shrink-0 bg-border\",\r\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n)\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,4TAAC,+QAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,+QAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf;KAVS", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/lib/api/orders.ts"], "sourcesContent": ["import { ApiResponse } from \"./products\";\n\nconst getApiBaseUrl = (): string => {\n  if (typeof window !== \"undefined\") {\n    // Client-side: use current origin or environment variable\n    return (\n      process.env.NEXT_PUBLIC_API_URL ||\n      `${window.location.protocol}//${window.location.hostname}:3011`\n    );\n  }\n  // Server-side: use environment variable or default\n  return process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3011\";\n};\n\nconst API_BASE_URL = getApiBaseUrl();\n\n/**\n * Order status enumeration\n */\nexport type OrderStatus =\n  | \"pending\"\n  | \"paid\"\n  | \"processing\"\n  | \"shipped\"\n  | \"delivered\"\n  | \"cancelled\"\n  | \"refunded\"\n  | \"returned\"\n  | \"failed\";\n\n/**\n * Shipping status enumeration\n */\nexport type ShippingStatus =\n  | \"pending\"\n  | \"preparing\"\n  | \"shipped\"\n  | \"in-transit\"\n  | \"delivered\"\n  | \"returned\"\n  | \"cancelled\";\n\n/**\n * Payment status enumeration\n */\nexport type PaymentStatus =\n  | \"pending\"\n  | \"paid\"\n  | \"failed\"\n  | \"refunded\"\n  | \"partially-refunded\";\n\n/**\n * Payment method enumeration\n */\nexport type PaymentMethod =\n  | \"card\"\n  | \"paypal\"\n  | \"bank-transfer\"\n  | \"cash-on-delivery\"\n  | \"crypto\";\n\n/**\n * Currency enumeration\n */\nexport type CurrencyUnit = \"EUR\" | \"USD\";\n\n/**\n * Order item interface\n */\nexport interface OrderItem {\n  productId: string;\n  name: string;\n  quantity: number;\n  price: number;\n  currency: CurrencyUnit;\n  image?: string;\n  sku?: string;\n  variant?: string;\n}\n\n/**\n * Customer information interface\n */\nexport interface Customer {\n  customerId?: string;\n  name: string;\n  email: string;\n  phone?: string;\n  avatar?: string;\n}\n\n/**\n * Address interface\n */\nexport interface Address {\n  street: string;\n  city: string;\n  state?: string;\n  postalCode: string;\n  country: string;\n  coordinates?: {\n    latitude: number;\n    longitude: number;\n  };\n}\n\n/**\n * Shipping information interface\n */\nexport interface Shipping {\n  method: string;\n  cost: number;\n  trackingNumber?: string;\n  estimatedDelivery?: string;\n  shippedAt?: string;\n  deliveredAt?: string;\n}\n\n/**\n * Payment information interface\n */\nexport interface Payment {\n  method: PaymentMethod;\n  status: PaymentStatus;\n  transactionId?: string;\n  providerTransactionId?: string;\n  cardLast4?: string;\n  cardType?: string;\n  receiptUrl?: string;\n  paidAt?: string;\n}\n\n/**\n * Order interface matching the backend Order type\n */\nexport interface Order {\n  _id: string;\n  orderNumber: string;\n  customer: Customer;\n  items: OrderItem[];\n\n  // Pricing\n  subtotal: number;\n  shippingCost: number;\n  tax: number;\n  discount: number;\n  totalAmount: number;\n  currency: CurrencyUnit;\n\n  // Status\n  status: OrderStatus;\n  paymentStatus: PaymentStatus;\n  shippingStatus: ShippingStatus;\n\n  // Addresses\n  shippingAddress: Address;\n  billingAddress?: Address;\n\n  // Payment & Shipping\n  payment: Payment;\n  shipping: Shipping;\n\n  // Additional info\n  notes?: string;\n  internalNotes?: string;\n  tags?: string[];\n\n  // Timestamps\n  placedAt: string;\n  estimatedDelivery?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n/**\n * DTO for creating a new order\n */\nexport interface CreateOrderDto {\n  customer: Customer;\n  items: OrderItem[];\n  shippingAddress: Address;\n  billingAddress?: Address;\n  shipping: {\n    method: string;\n    cost: number;\n  };\n  payment: {\n    method: PaymentMethod;\n    transactionId?: string;\n  };\n  notes?: string;\n  currency?: CurrencyUnit;\n}\n\n/**\n * DTO for updating an order\n */\nexport interface UpdateOrderDto {\n  status?: OrderStatus;\n  paymentStatus?: PaymentStatus;\n  shippingStatus?: ShippingStatus;\n  shipping?: Partial<Shipping>;\n  payment?: Partial<Payment>;\n  notes?: string;\n  internalNotes?: string;\n  tags?: string[];\n}\n\n/**\n * Order filters for querying\n */\nexport interface OrderFilters {\n  status?: OrderStatus | OrderStatus[];\n  paymentStatus?: PaymentStatus | PaymentStatus[];\n  shippingStatus?: ShippingStatus | ShippingStatus[];\n  customerId?: string;\n  customerEmail?: string;\n  orderNumber?: string;\n  dateFrom?: string;\n  dateTo?: string;\n  minAmount?: number;\n  maxAmount?: number;\n  search?: string;\n}\n\n/**\n * Order statistics interface\n */\nexport interface OrderStats {\n  totalOrders: number;\n  totalRevenue: number;\n  averageOrderValue: number;\n  ordersByStatus: Record<OrderStatus, number>;\n  ordersByPaymentStatus: Record<PaymentStatus, number>;\n  ordersByShippingStatus: Record<ShippingStatus, number>;\n  recentOrders: number;\n  pendingOrders: number;\n}\n\n/**\n * Paginated orders response\n */\nexport interface OrdersResponse {\n  orders: Order[];\n  total: number;\n  page: number;\n  totalPages: number;\n}\n\n/**\n * API response wrapper\n */\ninterface ApiResponse<T> {\n  success: boolean;\n  data: T;\n  message?: string;\n  error?: string;\n}\n\n/**\n * Get all orders with optional filtering and pagination\n */\nexport const getOrders = async (\n  filters: OrderFilters = {},\n  page: number = 1,\n  limit: number = 20,\n  sortBy: string = \"placedAt\",\n  sortOrder: \"asc\" | \"desc\" = \"desc\"\n): Promise<OrdersResponse> => {\n  const params = new URLSearchParams({\n    page: page.toString(),\n    limit: limit.toString(),\n    sortBy,\n    sortOrder,\n  });\n\n  // Add filters to params\n  Object.entries(filters).forEach(([key, value]) => {\n    if (value !== undefined && value !== null && value !== \"\") {\n      if (Array.isArray(value)) {\n        value.forEach((v) => params.append(key, v.toString()));\n      } else {\n        params.append(key, value.toString());\n      }\n    }\n  });\n\n  const response = await fetch(`${API_BASE_URL}/orders?${params}`);\n\n  if (!response.ok) {\n    throw new Error(`Failed to fetch orders: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<OrdersResponse> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to fetch orders\");\n  }\n\n  return result.data;\n};\n\n/**\n * Get order by ID\n */\nexport const getOrderById = async (orderId: string): Promise<Order> => {\n  const response = await fetch(`${API_BASE_URL}/orders/${orderId}`);\n\n  if (!response.ok) {\n    throw new Error(`Failed to fetch order: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<Order> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to fetch order\");\n  }\n\n  return result.data;\n};\n\n/**\n * Get order by order number\n */\nexport const getOrderByNumber = async (orderNumber: string): Promise<Order> => {\n  const response = await fetch(`${API_BASE_URL}/orders/number/${orderNumber}`);\n\n  if (!response.ok) {\n    throw new Error(`Failed to fetch order: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<Order> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to fetch order\");\n  }\n\n  return result.data;\n};\n\n/**\n * Create a new order\n */\nexport const createOrder = async (\n  orderData: CreateOrderDto\n): Promise<Order> => {\n  const response = await fetch(`${API_BASE_URL}/orders`, {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: JSON.stringify(orderData),\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to create order: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<Order> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to create order\");\n  }\n\n  return result.data;\n};\n\n/**\n * Update an order\n */\nexport const updateOrder = async (\n  orderId: string,\n  updateData: UpdateOrderDto\n): Promise<Order> => {\n  const response = await fetch(`${API_BASE_URL}/orders/${orderId}`, {\n    method: \"PUT\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: JSON.stringify(updateData),\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to update order: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<Order> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to update order\");\n  }\n\n  return result.data;\n};\n\n/**\n * Update order status\n */\nexport const updateOrderStatus = async (\n  orderId: string,\n  status: OrderStatus,\n  paymentStatus?: PaymentStatus,\n  shippingStatus?: ShippingStatus\n): Promise<Order> => {\n  const response = await fetch(`${API_BASE_URL}/orders/${orderId}/status`, {\n    method: \"PUT\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: JSON.stringify({ status, paymentStatus, shippingStatus }),\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to update order status: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<Order> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to update order status\");\n  }\n\n  return result.data;\n};\n\n/**\n * Delete (cancel) an order\n */\nexport const deleteOrder = async (orderId: string): Promise<void> => {\n  const response = await fetch(`${API_BASE_URL}/orders/${orderId}`, {\n    method: \"DELETE\",\n  });\n\n  if (!response.ok) {\n    throw new Error(`Failed to delete order: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<void> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to delete order\");\n  }\n};\n\n/**\n * Get order statistics\n */\nexport const getOrderStats = async (\n  dateFrom?: string,\n  dateTo?: string\n): Promise<OrderStats> => {\n  const params = new URLSearchParams();\n  if (dateFrom) params.append(\"dateFrom\", dateFrom);\n  if (dateTo) params.append(\"dateTo\", dateTo);\n\n  const response = await fetch(`${API_BASE_URL}/orders/stats?${params}`);\n\n  if (!response.ok) {\n    throw new Error(`Failed to fetch order statistics: ${response.statusText}`);\n  }\n\n  const result: ApiResponse<OrderStats> = await response.json();\n\n  if (!result.success) {\n    throw new Error(result.error || \"Failed to fetch order statistics\");\n  }\n\n  return result.data;\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAMM;AAJN,MAAM,gBAAgB;IACpB,wCAAmC;QACjC,0DAA0D;QAC1D,OACE,6DACA,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;IAEnE;;AAGF;AAEA,MAAM,eAAe;AAyPd,MAAM,YAAY,OACvB,UAAwB,CAAC,CAAC,EAC1B,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAAiB,UAAU,EAC3B,YAA4B,MAAM;IAElC,MAAM,SAAS,IAAI,gBAAgB;QACjC,MAAM,KAAK,QAAQ;QACnB,OAAO,MAAM,QAAQ;QACrB;QACA;IACF;IAEA,wBAAwB;IACxB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC3C,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;YACzD,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,MAAM,OAAO,CAAC,CAAC,IAAM,OAAO,MAAM,CAAC,KAAK,EAAE,QAAQ;YACpD,OAAO;gBACL,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;IACF;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ;IAE/D,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;IAClE;IAEA,MAAM,SAAsC,MAAM,SAAS,IAAI;IAE/D,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS;IAEhE,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;IACjE;IAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;IAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,mBAAmB,OAAO;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,EAAE,aAAa;IAE3E,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;IACjE;IAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;IAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,cAAc,OACzB;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,CAAC,EAAE;QACrD,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;IAClE;IAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;IAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,cAAc,OACzB,SACA;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;QAChE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;IAClE;IAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;IAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,oBAAoB,OAC/B,SACA,QACA,eACA;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,QAAQ,OAAO,CAAC,EAAE;QACvE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YAAE;YAAQ;YAAe;QAAe;IAC/D;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,UAAU,EAAE;IACzE;IAEA,MAAM,SAA6B,MAAM,SAAS,IAAI;IAEtD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB;AAKO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,EAAE,SAAS,EAAE;QAChE,QAAQ;IACV;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;IAClE;IAEA,MAAM,SAA4B,MAAM,SAAS,IAAI;IAErD,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;AACF;AAKO,MAAM,gBAAgB,OAC3B,UACA;IAEA,MAAM,SAAS,IAAI;IACnB,IAAI,UAAU,OAAO,MAAM,CAAC,YAAY;IACxC,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;IAEpC,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,EAAE,QAAQ;IAErE,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,SAAS,UAAU,EAAE;IAC5E;IAEA,MAAM,SAAkC,MAAM,SAAS,IAAI;IAE3D,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;IAClC;IAEA,OAAO,OAAO,IAAI;AACpB", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/hooks/useOrders.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from \"react\";\nimport { toast } from \"sonner\";\nimport {\n  Order,\n  OrdersResponse,\n  OrderFilters,\n  OrderStats,\n  CreateOrderDto,\n  UpdateOrderDto,\n  OrderStatus,\n  PaymentStatus,\n  ShippingStatus,\n  getOrders,\n  getOrderById,\n  getOrderByNumber,\n  createOrder,\n  updateOrder,\n  updateOrderStatus,\n  deleteOrder,\n  getOrderStats,\n} from \"@/lib/api/orders\";\n\n/**\n * Hook for fetching orders with filtering and pagination\n */\nexport const useOrders = (\n  initialFilters: OrderFilters = {},\n  initialPage: number = 1,\n  initialLimit: number = 20,\n  initialSortBy: string = \"placedAt\",\n  initialSortOrder: \"asc\" | \"desc\" = \"desc\"\n) => {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [total, setTotal] = useState(0);\n  const [page, setPage] = useState(initialPage);\n  const [totalPages, setTotalPages] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [filters, setFilters] = useState<OrderFilters>(initialFilters);\n  const [limit] = useState(initialLimit);\n  const [sortBy] = useState(initialSortBy);\n  const [sortOrder] = useState(initialSortOrder);\n\n  const fetchOrders = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await getOrders(filters, page, limit, sortBy, sortOrder);\n      setOrders(response.orders);\n      setTotal(response.total);\n      setTotalPages(response.totalPages);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch orders\";\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [filters, page, limit, sortBy, sortOrder]);\n\n  useEffect(() => {\n    fetchOrders();\n  }, [fetchOrders]);\n\n  const refetch = useCallback(() => {\n    fetchOrders();\n  }, [fetchOrders]);\n\n  const updateFilters = useCallback((newFilters: OrderFilters) => {\n    setFilters(newFilters);\n    setPage(1); // Reset to first page when filters change\n  }, []);\n\n  const updatePage = useCallback((newPage: number) => {\n    setPage(newPage);\n  }, []);\n\n  return {\n    orders,\n    total,\n    page,\n    totalPages,\n    loading,\n    error,\n    filters,\n    refetch,\n    updateFilters,\n    updatePage,\n  };\n};\n\n/**\n * Hook for fetching a single order by ID\n */\nexport const useOrder = (orderId: string | null) => {\n  const [order, setOrder] = useState<Order | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchOrder = useCallback(async () => {\n    if (!orderId) return;\n\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const orderData = await getOrderById(orderId);\n      setOrder(orderData);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch order\";\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [orderId]);\n\n  useEffect(() => {\n    fetchOrder();\n  }, [fetchOrder]);\n\n  const refetch = useCallback(() => {\n    fetchOrder();\n  }, [fetchOrder]);\n\n  return {\n    order,\n    loading,\n    error,\n    refetch,\n  };\n};\n\n/**\n * Hook for fetching a single order by order number\n */\nexport const useOrderByNumber = (orderNumber: string | null) => {\n  const [order, setOrder] = useState<Order | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchOrder = useCallback(async () => {\n    if (!orderNumber) return;\n\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const orderData = await getOrderByNumber(orderNumber);\n      setOrder(orderData);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch order\";\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [orderNumber]);\n\n  useEffect(() => {\n    fetchOrder();\n  }, [fetchOrder]);\n\n  const refetch = useCallback(() => {\n    fetchOrder();\n  }, [fetchOrder]);\n\n  return {\n    order,\n    loading,\n    error,\n    refetch,\n  };\n};\n\n/**\n * Hook for order mutations (create, update, delete)\n */\nexport const useOrderMutations = () => {\n  const [loading, setLoading] = useState(false);\n\n  const handleCreateOrder = useCallback(async (orderData: CreateOrderDto): Promise<Order> => {\n    setLoading(true);\n    try {\n      const newOrder = await createOrder(orderData);\n      toast.success(\"Order created successfully\");\n      return newOrder;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to create order\";\n      toast.error(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const handleUpdateOrder = useCallback(async (orderId: string, updateData: UpdateOrderDto): Promise<Order> => {\n    setLoading(true);\n    try {\n      const updatedOrder = await updateOrder(orderId, updateData);\n      toast.success(\"Order updated successfully\");\n      return updatedOrder;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to update order\";\n      toast.error(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const handleUpdateOrderStatus = useCallback(async (\n    orderId: string,\n    status: OrderStatus,\n    paymentStatus?: PaymentStatus,\n    shippingStatus?: ShippingStatus\n  ): Promise<Order> => {\n    setLoading(true);\n    try {\n      const updatedOrder = await updateOrderStatus(orderId, status, paymentStatus, shippingStatus);\n      toast.success(\"Order status updated successfully\");\n      return updatedOrder;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to update order status\";\n      toast.error(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const handleDeleteOrder = useCallback(async (orderId: string): Promise<void> => {\n    setLoading(true);\n    try {\n      await deleteOrder(orderId);\n      toast.success(\"Order cancelled successfully\");\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to cancel order\";\n      toast.error(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  return {\n    loading,\n    createOrder: handleCreateOrder,\n    updateOrder: handleUpdateOrder,\n    updateOrderStatus: handleUpdateOrderStatus,\n    deleteOrder: handleDeleteOrder,\n  };\n};\n\n/**\n * Hook for fetching order statistics\n */\nexport const useOrderStats = (dateFrom?: string, dateTo?: string) => {\n  const [stats, setStats] = useState<OrderStats | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchStats = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      const statsData = await getOrderStats(dateFrom, dateTo);\n      setStats(statsData);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Failed to fetch order statistics\";\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [dateFrom, dateTo]);\n\n  useEffect(() => {\n    fetchStats();\n  }, [fetchStats]);\n\n  const refetch = useCallback(() => {\n    fetchStats();\n  }, [fetchStats]);\n\n  return {\n    stats,\n    loading,\n    error,\n    refetch,\n  };\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAuBO,MAAM,YAAY,CACvB,iBAA+B,CAAC,CAAC,EACjC,cAAsB,CAAC,EACvB,eAAuB,EAAE,EACzB,gBAAwB,UAAU,EAClC,mBAAmC,MAAM;;IAEzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAgB;IACrD,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzB,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC1B,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE7B,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;8CAAE;YAC9B,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM,OAAO,QAAQ;gBAC/D,UAAU,SAAS,MAAM;gBACzB,SAAS,SAAS,KAAK;gBACvB,cAAc,SAAS,UAAU;YACnC,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;6CAAG;QAAC;QAAS;QAAM;QAAO;QAAQ;KAAU;IAE5C,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;QAAC;KAAY;IAEhB,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;0CAAE;YAC1B;QACF;yCAAG;QAAC;KAAY;IAEhB,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YACjC,WAAW;YACX,QAAQ,IAAI,0CAA0C;QACxD;+CAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YAC9B,QAAQ;QACV;4CAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAjEa;AAsEN,MAAM,WAAW,CAAC;;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4CAAE;YAC7B,IAAI,CAAC,SAAS;YAEd,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,YAAY,MAAM,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD,EAAE;gBACrC,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;2CAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG;QAAC;KAAW;IAEf,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;yCAAE;YAC1B;QACF;wCAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IArCa;AA0CN,MAAM,mBAAmB,CAAC;;IAC/B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;oDAAE;YAC7B,IAAI,CAAC,aAAa;YAElB,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,YAAY,MAAM,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE;gBACzC,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;mDAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAW;IAEf,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;iDAAE;YAC1B;QACF;gDAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IArCa;AA0CN,MAAM,oBAAoB;;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4DAAE,OAAO;YAC3C,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;gBACnC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;2DAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4DAAE,OAAO,SAAiB;YAC5D,WAAW;YACX,IAAI;gBACF,MAAM,eAAe,MAAM,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,SAAS;gBAChD,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;2DAAG,EAAE;IAEL,MAAM,0BAA0B,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kEAAE,OAC1C,SACA,QACA,eACA;YAEA,WAAW;YACX,IAAI;gBACF,MAAM,eAAe,MAAM,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,QAAQ,eAAe;gBAC7E,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;iEAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4DAAE,OAAO;YAC3C,WAAW;YACX,IAAI;gBACF,MAAM,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;gBAClB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;2DAAG,EAAE;IAEL,OAAO;QACL;QACA,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,aAAa;IACf;AACF;IA1Ea;AA+EN,MAAM,gBAAgB,CAAC,UAAmB;;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAqB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;iDAAE;YAC7B,WAAW;YACX,SAAS;YAET,IAAI;gBACF,MAAM,YAAY,MAAM,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;gBAChD,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;gDAAG;QAAC;QAAU;KAAO;IAErB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAW;IAEf,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;8CAAE;YAC1B;QACF;6CAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IAnCa", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Learning/Codes/dapp/client/components/pages/orders/details/OrderDetailsWrapper.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\nimport {\r\n  AlertCircle,\r\n  ArrowLeft,\r\n  Calendar,\r\n  CheckCircle,\r\n  Clock,\r\n  CreditCard,\r\n  Download,\r\n  Edit,\r\n  Mail,\r\n  MapPin,\r\n  MoreHorizontal,\r\n  Package,\r\n  Phone,\r\n  Printer,\r\n  RefreshCw,\r\n  Truck,\r\n  User,\r\n} from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { useOrder } from \"@/hooks/useOrders\";\r\n\r\ntype OrderDetailsWrapperProps = {\r\n  orderId: string;\r\n};\r\n\r\nexport const OrderDetailsWrapper = ({ orderId }: OrderDetailsWrapperProps) => {\r\n  const router = useRouter();\r\n  const { order, loading, error } = useOrder(orderId);\r\n\r\n  const handleGoBack = () => {\r\n    router.push(\"/admin/orders/list\");\r\n  };\r\n\r\n  const handleEditOrder = () => {\r\n    console.log(\"Edit order:\", orderId);\r\n  };\r\n\r\n  const handlePrintOrder = () => {\r\n    window.print();\r\n  };\r\n\r\n  const handleCancelOrder = () => {\r\n    console.log(\"Cancel order:\", orderId);\r\n  };\r\n\r\n  const handleRefundOrder = () => {\r\n    console.log(\"Refund order:\", orderId);\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center gap-4\">\r\n            <Skeleton className=\"h-9 w-32\" />\r\n            <div className=\"space-y-2\">\r\n              <Skeleton className=\"h-8 w-48\" />\r\n              <Skeleton className=\"h-4 w-64\" />\r\n            </div>\r\n          </div>\r\n          <div className=\"flex gap-2\">\r\n            <Skeleton className=\"h-9 w-20\" />\r\n            <Skeleton className=\"h-9 w-24\" />\r\n            <Skeleton className=\"h-9 w-10\" />\r\n          </div>\r\n        </div>\r\n        <div className=\"grid gap-4 md:grid-cols-3\">\r\n          {Array.from({ length: 3 }).map((_, i) => (\r\n            <Card key={i}>\r\n              <CardContent className=\"p-6\">\r\n                <Skeleton className=\"h-16 w-full\" />\r\n              </CardContent>\r\n            </Card>\r\n          ))}\r\n        </div>\r\n        <div className=\"grid gap-6 lg:grid-cols-3\">\r\n          <div className=\"lg:col-span-2\">\r\n            <Card>\r\n              <CardHeader>\r\n                <Skeleton className=\"h-6 w-32\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <Skeleton className=\"h-64 w-full\" />\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n          <div className=\"space-y-6\">\r\n            {Array.from({ length: 4 }).map((_, i) => (\r\n              <Card key={i}>\r\n                <CardHeader>\r\n                  <Skeleton className=\"h-6 w-40\" />\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <Skeleton className=\"h-20 w-full\" />\r\n                </CardContent>\r\n              </Card>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handleGoBack}\r\n            className=\"flex items-center gap-2\"\r\n          >\r\n            <ArrowLeft className=\"h-4 w-4\" />\r\n            Back to Orders\r\n          </Button>\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold text-gray-900\">\r\n              Order Not Found\r\n            </h1>\r\n            <p className=\"text-sm text-red-600\">Error: {error}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!order) {\r\n    return null;\r\n  }\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case \"completed\":\r\n        return \"bg-green-100 text-green-800\";\r\n      case \"processing\":\r\n        return \"bg-blue-100 text-blue-800\";\r\n      case \"pending\":\r\n        return \"bg-yellow-100 text-yellow-800\";\r\n      case \"cancelled\":\r\n        return \"bg-red-100 text-red-800\";\r\n      default:\r\n        return \"bg-gray-100 text-gray-800\";\r\n    }\r\n  };\r\n\r\n  const getPaymentStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case \"paid\":\r\n        return \"bg-green-100 text-green-800\";\r\n      case \"pending\":\r\n        return \"bg-yellow-100 text-yellow-800\";\r\n      case \"failed\":\r\n        return \"bg-red-100 text-red-800\";\r\n      default:\r\n        return \"bg-gray-100 text-gray-800\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header Section */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handleGoBack}\r\n            className=\"flex items-center gap-2\"\r\n          >\r\n            <ArrowLeft className=\"h-4 w-4\" />\r\n            Back to Orders\r\n          </Button>\r\n\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold text-gray-900\">\r\n              Order #{order.orderNumber}\r\n            </h1>\r\n            <p className=\"text-sm text-gray-500\">\r\n              Placed on{\" \"}\r\n              {new Date(order.placedAt).toLocaleDateString(\"en-US\", {\r\n                year: \"numeric\",\r\n                month: \"long\",\r\n                day: \"numeric\",\r\n                hour: \"2-digit\",\r\n                minute: \"2-digit\",\r\n              })}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex gap-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handlePrintOrder}\r\n            className=\"flex items-center gap-2\"\r\n          >\r\n            <Printer className=\"h-4 w-4\" />\r\n            Print\r\n          </Button>\r\n\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handleEditOrder}\r\n            className=\"flex items-center gap-2\"\r\n          >\r\n            <Edit className=\"h-4 w-4\" />\r\n            Edit Order\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"outline\" size=\"sm\">\r\n                <MoreHorizontal className=\"h-4 w-4\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\">\r\n              <DropdownMenuItem onClick={handleRefundOrder}>\r\n                <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n                Refund Order\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem>\r\n                <Download className=\"mr-2 h-4 w-4\" />\r\n                Download Invoice\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                className=\"text-red-600\"\r\n                onClick={handleCancelOrder}\r\n              >\r\n                <AlertCircle className=\"mr-2 h-4 w-4\" />\r\n                Cancel Order\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Status Overview Cards */}\r\n      <div className=\"grid gap-4 md:grid-cols-3\">\r\n        <Card>\r\n          <CardContent className=\"p-6\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-blue-100 p-2\">\r\n                <Package className=\"h-5 w-5 text-blue-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm font-medium text-gray-600\">\r\n                  Order Status\r\n                </p>\r\n                <Badge className={`mt-1 ${getStatusColor(order.status)}`}>\r\n                  {order.status.charAt(0).toUpperCase() + order.status.slice(1)}\r\n                </Badge>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardContent className=\"p-6\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-green-100 p-2\">\r\n                <CreditCard className=\"h-5 w-5 text-green-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm font-medium text-gray-600\">\r\n                  Payment Status\r\n                </p>\r\n                <Badge\r\n                  className={`mt-1 ${getPaymentStatusColor(order.paymentStatus)}`}\r\n                >\r\n                  {order.paymentStatus.charAt(0).toUpperCase() +\r\n                    order.paymentStatus.slice(1)}\r\n                </Badge>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card>\r\n          <CardContent className=\"p-6\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"rounded-full bg-purple-100 p-2\">\r\n                <Truck className=\"h-5 w-5 text-purple-600\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm font-medium text-gray-600\">\r\n                  Estimated Delivery\r\n                </p>\r\n                <p className=\"mt-1 font-semibold text-gray-900\">\r\n                  {order.estimatedDelivery\r\n                    ? new Date(order.estimatedDelivery).toLocaleDateString(\r\n                        \"en-US\",\r\n                        {\r\n                          month: \"short\",\r\n                          day: \"numeric\",\r\n                        }\r\n                      )\r\n                    : \"TBD\"}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Main Content Grid */}\r\n      <div className=\"grid gap-6 lg:grid-cols-3\">\r\n        {/* Order Items - Takes 2 columns */}\r\n        <div className=\"lg:col-span-2\">\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <Package className=\"h-5 w-5 text-blue-600\" />\r\n                Order Items ({order.items.length})\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"space-y-4\">\r\n                {order.items.map((item, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className=\"flex items-center gap-4 rounded-lg border p-4\"\r\n                  >\r\n                    <div className=\"h-16 w-16 overflow-hidden rounded-lg bg-gray-100\">\r\n                      {item.image ? (\r\n                        <Image\r\n                          src={item.image}\r\n                          alt={item.name}\r\n                          width={64}\r\n                          height={64}\r\n                          className=\"object-cover\"\r\n                        />\r\n                      ) : (\r\n                        <div className=\"flex h-full w-full items-center justify-center\">\r\n                          <Package className=\"h-6 w-6 text-gray-400\" />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div className=\"flex-1\">\r\n                      <h4 className=\"font-medium text-gray-900\">{item.name}</h4>\r\n                      <p className=\"text-sm text-gray-500\">\r\n                        Quantity: {item.quantity}\r\n                      </p>\r\n                      {item.sku && (\r\n                        <p className=\"text-xs text-gray-400\">SKU: {item.sku}</p>\r\n                      )}\r\n                    </div>\r\n\r\n                    <div className=\"text-right\">\r\n                      <p className=\"font-medium text-gray-900\">\r\n                        {order.currency === \"EUR\" ? \"€\" : \"$\"}\r\n                        {(item.price * item.quantity).toFixed(2)}\r\n                      </p>\r\n                      <p className=\"text-sm text-gray-500\">\r\n                        {order.currency === \"EUR\" ? \"€\" : \"$\"}\r\n                        {item.price.toFixed(2)} each\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              <Separator className=\"my-6\" />\r\n\r\n              {/* Order Summary */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex justify-between text-sm\">\r\n                  <span className=\"text-gray-600\">Subtotal</span>\r\n                  <span className=\"text-gray-900\">\r\n                    {order.currency === \"EUR\" ? \"€\" : \"$\"}\r\n                    {order.subtotal.toFixed(2)}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between text-sm\">\r\n                  <span className=\"text-gray-600\">Shipping</span>\r\n                  <span className=\"text-gray-900\">\r\n                    {order.currency === \"EUR\" ? \"€\" : \"$\"}\r\n                    {order.shippingCost.toFixed(2)}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex justify-between text-sm\">\r\n                  <span className=\"text-gray-600\">Tax</span>\r\n                  <span className=\"text-gray-900\">\r\n                    {order.currency === \"EUR\" ? \"€\" : \"$\"}\r\n                    {order.tax.toFixed(2)}\r\n                  </span>\r\n                </div>\r\n                {order.discount > 0 && (\r\n                  <div className=\"flex justify-between text-sm\">\r\n                    <span className=\"text-gray-600\">Discount</span>\r\n                    <span className=\"text-green-600\">\r\n                      -{order.currency === \"EUR\" ? \"€\" : \"$\"}\r\n                      {order.discount.toFixed(2)}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n                <Separator className=\"my-2\" />\r\n                <div className=\"flex justify-between text-lg font-semibold\">\r\n                  <span className=\"text-gray-900\">Total</span>\r\n                  <span className=\"text-gray-900\">\r\n                    {order.currency === \"EUR\" ? \"€\" : \"$\"}\r\n                    {order.totalAmount.toFixed(2)}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Sidebar - Customer & Shipping Info */}\r\n        <div className=\"space-y-6\">\r\n          {/* Customer Information */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <User className=\"h-5 w-5 text-blue-600\" />\r\n                Customer Information\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"flex h-10 w-10 items-center justify-center rounded-full bg-blue-100\">\r\n                  <User className=\"h-5 w-5 text-blue-600\" />\r\n                </div>\r\n                <div>\r\n                  <p className=\"font-medium text-gray-900\">\r\n                    {order.customer.name}\r\n                  </p>\r\n                  <p className=\"text-sm text-gray-500\">Customer</p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Mail className=\"h-4 w-4 text-gray-400\" />\r\n                  <span className=\"text-sm text-gray-600\">\r\n                    {order.customer.email}\r\n                  </span>\r\n                </div>\r\n                {order.customer.phone && (\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Phone className=\"h-4 w-4 text-gray-400\" />\r\n                    <span className=\"text-sm text-gray-600\">\r\n                      {order.customer.phone}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Shipping Address */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <MapPin className=\"h-5 w-5 text-green-600\" />\r\n                Shipping Address\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"space-y-1 text-sm text-gray-600\">\r\n                <p className=\"font-medium text-gray-900\">\r\n                  {order.customer.name}\r\n                </p>\r\n                <p>{order.shippingAddress.street}</p>\r\n                <p>\r\n                  {order.shippingAddress.city}\r\n                  {order.shippingAddress.state &&\r\n                    `, ${order.shippingAddress.state}`}\r\n                  {order.shippingAddress.postalCode &&\r\n                    `, ${order.shippingAddress.postalCode}`}\r\n                </p>\r\n                <p>{order.shippingAddress.country}</p>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Payment Information */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <CreditCard className=\"h-5 w-5 text-purple-600\" />\r\n                Payment Information\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <div className=\"flex h-8 w-12 items-center justify-center rounded bg-gray-100\">\r\n                  <CreditCard className=\"h-4 w-4 text-gray-600\" />\r\n                </div>\r\n                <div>\r\n                  <p className=\"font-medium text-gray-900\">\r\n                    {order.payment.cardType && order.payment.cardLast4\r\n                      ? `${order.payment.cardType} •••• ${order.payment.cardLast4}`\r\n                      : order.payment.method.charAt(0).toUpperCase() +\r\n                        order.payment.method.slice(1)}\r\n                  </p>\r\n                  <p className=\"text-sm text-gray-500\">\r\n                    {order.payment.method.charAt(0).toUpperCase() +\r\n                      order.payment.method.slice(1)}\r\n                  </p>\r\n                  {order.payment.transactionId && (\r\n                    <p className=\"text-xs text-gray-400\">\r\n                      Transaction: {order.payment.transactionId}\r\n                    </p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Shipping Information */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <Truck className=\"h-5 w-5 text-orange-600\" />\r\n                Shipping Information\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-3\">\r\n              <div>\r\n                <p className=\"font-medium text-gray-900\">\r\n                  {order.shipping.method}\r\n                </p>\r\n                <p className=\"text-sm text-gray-500\">\r\n                  Shipping Cost: {order.currency === \"EUR\" ? \"€\" : \"$\"}\r\n                  {order.shippingCost.toFixed(2)}\r\n                </p>\r\n              </div>\r\n\r\n              {order.shipping.trackingNumber && (\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-gray-700\">\r\n                    Tracking Number\r\n                  </p>\r\n                  <p className=\"font-mono text-sm text-blue-600\">\r\n                    {order.shipping.trackingNumber}\r\n                  </p>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"flex items-center gap-2 text-sm\">\r\n                <Calendar className=\"h-4 w-4 text-gray-400\" />\r\n                <span className=\"text-gray-600\">\r\n                  Est. delivery:{\" \"}\r\n                  {order.estimatedDelivery\r\n                    ? new Date(order.estimatedDelivery).toLocaleDateString(\r\n                        \"en-US\",\r\n                        {\r\n                          month: \"long\",\r\n                          day: \"numeric\",\r\n                        }\r\n                      )\r\n                    : \"TBD\"}\r\n                </span>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;AACA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;;;AAtCA;;;;;;;;;;;AA4CO,MAAM,sBAAsB,CAAC,EAAE,OAAO,EAA4B;;IACvE,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,QAAQ,GAAG,CAAC,eAAe;IAC7B;IAEA,MAAM,mBAAmB;QACvB,OAAO,KAAK;IACd;IAEA,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,IAAI,SAAS;QACX,qBACE,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,4TAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;sCAGxB,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,4TAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,4TAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAGxB,4TAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,4TAAC,4HAAA,CAAA,OAAI;sCACH,cAAA,4TAAC,4HAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;2BAFb;;;;;;;;;;8BAOf,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC,4HAAA,CAAA,OAAI;;kDACH,4TAAC,4HAAA,CAAA,aAAU;kDACT,cAAA,4TAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,4TAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,4TAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAI1B,4TAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,4TAAC,4HAAA,CAAA,OAAI;;sDACH,4TAAC,4HAAA,CAAA,aAAU;sDACT,cAAA,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,4TAAC,4HAAA,CAAA,cAAW;sDACV,cAAA,4TAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;mCALb;;;;;;;;;;;;;;;;;;;;;;IAavB;IAEA,IAAI,OAAO;QACT,qBACE,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCACb,4TAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,4TAAC,uSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGnC,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,4TAAC;gCAAE,WAAU;;oCAAuB;oCAAQ;;;;;;;;;;;;;;;;;;;;;;;;IAKtD;IAEA,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,4TAAC,uSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAInC,4TAAC;;kDACC,4TAAC;wCAAG,WAAU;;4CAAmC;4CACvC,MAAM,WAAW;;;;;;;kDAE3B,4TAAC;wCAAE,WAAU;;4CAAwB;4CACzB;4CACT,IAAI,KAAK,MAAM,QAAQ,EAAE,kBAAkB,CAAC,SAAS;gDACpD,MAAM;gDACN,OAAO;gDACP,KAAK;gDACL,MAAM;gDACN,QAAQ;4CACV;;;;;;;;;;;;;;;;;;;kCAKN,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,4TAAC,+RAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAIjC,4TAAC,8HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,4TAAC,kSAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAI9B,4TAAC,wIAAA,CAAA,eAAY;;kDACX,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,4TAAC,8HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAC7B,cAAA,4TAAC,uSAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG9B,4TAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,4TAAC,wIAAA,CAAA,mBAAgB;gDAAC,SAAS;;kEACzB,4TAAC,uSAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,4TAAC,wIAAA,CAAA,mBAAgB;;kEACf,4TAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,4TAAC,wIAAA,CAAA,wBAAqB;;;;;0DACtB,4TAAC,wIAAA,CAAA,mBAAgB;gDACf,WAAU;gDACV,SAAS;;kEAET,4TAAC,2SAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlD,4TAAC;gBAAI,WAAU;;kCACb,4TAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,4TAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC,+RAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,4TAAC;;0DACC,4TAAC;gDAAE,WAAU;0DAAoC;;;;;;0DAGjD,4TAAC,6HAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,KAAK,EAAE,eAAe,MAAM,MAAM,GAAG;0DACrD,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOrE,4TAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,4TAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC,ySAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,4TAAC;;0DACC,4TAAC;gDAAE,WAAU;0DAAoC;;;;;;0DAGjD,4TAAC,6HAAA,CAAA,QAAK;gDACJ,WAAW,CAAC,KAAK,EAAE,sBAAsB,MAAM,aAAa,GAAG;0DAE9D,MAAM,aAAa,CAAC,MAAM,CAAC,GAAG,WAAW,KACxC,MAAM,aAAa,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtC,4TAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,4TAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,4TAAC;;0DACC,4TAAC;gDAAE,WAAU;0DAAoC;;;;;;0DAGjD,4TAAC;gDAAE,WAAU;0DACV,MAAM,iBAAiB,GACpB,IAAI,KAAK,MAAM,iBAAiB,EAAE,kBAAkB,CAClD,SACA;oDACE,OAAO;oDACP,KAAK;gDACP,KAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShB,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC,4HAAA,CAAA,OAAI;;8CACH,4TAAC,4HAAA,CAAA,aAAU;8CACT,cAAA,4TAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,4TAAC,+RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA0B;4CAC/B,MAAM,KAAK,CAAC,MAAM;4CAAC;;;;;;;;;;;;8CAGrC,4TAAC,4HAAA,CAAA,cAAW;;sDACV,4TAAC;4CAAI,WAAU;sDACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtB,4TAAC;oDAEC,WAAU;;sEAEV,4TAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK,iBACT,4TAAC,+PAAA,CAAA,UAAK;gEACJ,KAAK,KAAK,KAAK;gEACf,KAAK,KAAK,IAAI;gEACd,OAAO;gEACP,QAAQ;gEACR,WAAU;;;;;qFAGZ,4TAAC;gEAAI,WAAU;0EACb,cAAA,4TAAC,+RAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAKzB,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAG,WAAU;8EAA6B,KAAK,IAAI;;;;;;8EACpD,4TAAC;oEAAE,WAAU;;wEAAwB;wEACxB,KAAK,QAAQ;;;;;;;gEAEzB,KAAK,GAAG,kBACP,4TAAC;oEAAE,WAAU;;wEAAwB;wEAAM,KAAK,GAAG;;;;;;;;;;;;;sEAIvD,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAE,WAAU;;wEACV,MAAM,QAAQ,KAAK,QAAQ,MAAM;wEACjC,CAAC,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC;;;;;;;8EAExC,4TAAC;oEAAE,WAAU;;wEACV,MAAM,QAAQ,KAAK,QAAQ,MAAM;wEACjC,KAAK,KAAK,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;mDApCtB;;;;;;;;;;sDA2CX,4TAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDAGrB,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,4TAAC;4DAAK,WAAU;;gEACb,MAAM,QAAQ,KAAK,QAAQ,MAAM;gEACjC,MAAM,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;;8DAG5B,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,4TAAC;4DAAK,WAAU;;gEACb,MAAM,QAAQ,KAAK,QAAQ,MAAM;gEACjC,MAAM,YAAY,CAAC,OAAO,CAAC;;;;;;;;;;;;;8DAGhC,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,4TAAC;4DAAK,WAAU;;gEACb,MAAM,QAAQ,KAAK,QAAQ,MAAM;gEACjC,MAAM,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;;gDAGtB,MAAM,QAAQ,GAAG,mBAChB,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,4TAAC;4DAAK,WAAU;;gEAAiB;gEAC7B,MAAM,QAAQ,KAAK,QAAQ,MAAM;gEAClC,MAAM,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;;8DAI9B,4TAAC,iIAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,4TAAC;4DAAK,WAAU;;gEACb,MAAM,QAAQ,KAAK,QAAQ,MAAM;gEACjC,MAAM,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASvC,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC,4HAAA,CAAA,OAAI;;kDACH,4TAAC,4HAAA,CAAA,aAAU;kDACT,cAAA,4TAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,4TAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAA0B;;;;;;;;;;;;kDAI9C,4TAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;kEACb,cAAA,4TAAC,yRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,4TAAC;;0EACC,4TAAC;gEAAE,WAAU;0EACV,MAAM,QAAQ,CAAC,IAAI;;;;;;0EAEtB,4TAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,4TAAC;gEAAK,WAAU;0EACb,MAAM,QAAQ,CAAC,KAAK;;;;;;;;;;;;oDAGxB,MAAM,QAAQ,CAAC,KAAK,kBACnB,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,2RAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,4TAAC;gEAAK,WAAU;0EACb,MAAM,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASjC,4TAAC,4HAAA,CAAA,OAAI;;kDACH,4TAAC,4HAAA,CAAA,aAAU;kDACT,cAAA,4TAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,4TAAC,iSAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;;;;;;kDAIjD,4TAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAE,WAAU;8DACV,MAAM,QAAQ,CAAC,IAAI;;;;;;8DAEtB,4TAAC;8DAAG,MAAM,eAAe,CAAC,MAAM;;;;;;8DAChC,4TAAC;;wDACE,MAAM,eAAe,CAAC,IAAI;wDAC1B,MAAM,eAAe,CAAC,KAAK,IAC1B,CAAC,EAAE,EAAE,MAAM,eAAe,CAAC,KAAK,EAAE;wDACnC,MAAM,eAAe,CAAC,UAAU,IAC/B,CAAC,EAAE,EAAE,MAAM,eAAe,CAAC,UAAU,EAAE;;;;;;;8DAE3C,4TAAC;8DAAG,MAAM,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;0CAMvC,4TAAC,4HAAA,CAAA,OAAI;;kDACH,4TAAC,4HAAA,CAAA,aAAU;kDACT,cAAA,4TAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,4TAAC,ySAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAA4B;;;;;;;;;;;;kDAItD,4TAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;8DACb,cAAA,4TAAC,ySAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,4TAAC;;sEACC,4TAAC;4DAAE,WAAU;sEACV,MAAM,OAAO,CAAC,QAAQ,IAAI,MAAM,OAAO,CAAC,SAAS,GAC9C,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC,SAAS,EAAE,GAC3D,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAC1C,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;;;;;;sEAEjC,4TAAC;4DAAE,WAAU;sEACV,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KACzC,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;;;;;;wDAE9B,MAAM,OAAO,CAAC,aAAa,kBAC1B,4TAAC;4DAAE,WAAU;;gEAAwB;gEACrB,MAAM,OAAO,CAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASrD,4TAAC,4HAAA,CAAA,OAAI;;kDACH,4TAAC,4HAAA,CAAA,aAAU;kDACT,cAAA,4TAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,4TAAC,2RAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAA4B;;;;;;;;;;;;kDAIjD,4TAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,4TAAC;;kEACC,4TAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ,CAAC,MAAM;;;;;;kEAExB,4TAAC;wDAAE,WAAU;;4DAAwB;4DACnB,MAAM,QAAQ,KAAK,QAAQ,MAAM;4DAChD,MAAM,YAAY,CAAC,OAAO,CAAC;;;;;;;;;;;;;4CAI/B,MAAM,QAAQ,CAAC,cAAc,kBAC5B,4TAAC;;kEACC,4TAAC;wDAAE,WAAU;kEAAoC;;;;;;kEAGjD,4TAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ,CAAC,cAAc;;;;;;;;;;;;0DAKpC,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,4TAAC;wDAAK,WAAU;;4DAAgB;4DACf;4DACd,MAAM,iBAAiB,GACpB,IAAI,KAAK,MAAM,iBAAiB,EAAE,kBAAkB,CAClD,SACA;gEACE,OAAO;gEACP,KAAK;4DACP,KAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB;GA7hBa;;QACI,oQAAA,CAAA,YAAS;QACU,qHAAA,CAAA,WAAQ;;;KAF/B", "debugId": null}}]}